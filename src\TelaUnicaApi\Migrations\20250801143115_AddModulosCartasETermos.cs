﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace TelaUnica.Infra.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class AddModulosCartasETermos : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {

            migrationBuilder.InsertData(
                table: "Modules",
                columns: new[] { "Id", "CreatedAt", "CreatedUserId", "DeletedAt", "Name", "UpdatedAt", "UpdatedUserId" },
                values: new object[] { 26, new DateTime(2025, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), 1, null, "Cartas e Termos", new DateTime(2025, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), 1 });

            migrationBuilder.InsertData(
                table: "SubModules",
                columns: new[] { "Id", "CreatedAt", "CreatedUserId", "DeletedAt", "ModuleId", "Name", "UpdatedAt", "UpdatedUserId" },
                values: new object[,]
                {
                    { 55, new DateTime(2023, 8, 4, 14, 49, 0, 0, DateTimeKind.Unspecified), 1, null, 26, "Fila de Aprovação", new DateTime(2023, 8, 4, 14, 49, 0, 0, DateTimeKind.Unspecified), 1 },
                    { 56, new DateTime(2023, 8, 4, 14, 49, 0, 0, DateTimeKind.Unspecified), 1, null, 26, "Solicitar Termos Jurídicos", new DateTime(2023, 8, 4, 14, 49, 0, 0, DateTimeKind.Unspecified), 1 }
                });

        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

            migrationBuilder.DeleteData(
                table: "SubModules",
                keyColumn: "Id",
                keyValue: 55);

            migrationBuilder.DeleteData(
                table: "SubModules",
                keyColumn: "Id",
                keyValue: 56);

            migrationBuilder.DeleteData(
                table: "Modules",
                keyColumn: "Id",
                keyValue: 26);

        }
    }
}
