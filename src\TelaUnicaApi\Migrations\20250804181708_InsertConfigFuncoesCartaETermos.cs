﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace TelaUnica.Infra.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class InsertConfigFuncoesCartaETermos : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "Configs",
                columns: new[] { "Id", "CreatedAt", "CreatedUserId", "DeletedAt", "Encrypted", "Key", "Obs", "UpdatedAt", "UpdatedUserId", "Value" },
                values: new object[,]
                {
                    { new Guid("7dd53e1c-9ae3-4d98-bc67-da82c158a0b3"), new DateTime(2025, 8, 3, 0, 0, 0, 0, DateTimeKind.Local), 1, null, null, "funcoes_backoffice", null, new DateTime(2025, 8, 3, 0, 0, 0, 0, DateTimeKind.Local), 1, "" },
                    { new Guid("c6fac9e7-da9f-46a1-a31d-e852f5289014"), new DateTime(2025, 8, 3, 0, 0, 0, 0, DateTimeKind.Local), 1, null, null, "funcoes_juridicas", null, new DateTime(2025, 8, 3, 0, 0, 0, 0, DateTimeKind.Local), 1, "" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Configs",
                keyColumn: "Id",
                keyValue: new Guid("7dd53e1c-9ae3-4d98-bc67-da82c158a0b3"));

            migrationBuilder.DeleteData(
                table: "Configs",
                keyColumn: "Id",
                keyValue: new Guid("c6fac9e7-da9f-46a1-a31d-e852f5289014"));
        }
    }
}
