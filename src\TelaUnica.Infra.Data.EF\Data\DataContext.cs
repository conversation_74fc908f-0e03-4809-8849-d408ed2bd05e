using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.Globalization;
using System.Security.Cryptography;
using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.Models;
using TelaUnica.Infra.Data.EF.Models.Agreement;
using TelaUnica.Infra.Data.EF.Models.CartasETermos;
using TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;
using TelaUnica.Infra.Data.EF.Models.Negotiation;
using TelaUnica.Infra.Data.EF.Models.Negotiation.Agreements;
using TelaUnica.Infra.Data.EF.Models.Negotiation.Contracts;
using TelaUnica.Infra.Data.EF.Models.Negotiation.Financed;
using TelaUnica.Infra.Data.EF.Models.Negotiation.Invoice;
using TelaUnica.Infra.Data.EF.Models.Negotiation.TradingRestriction;
using TelaUnica.Infra.Data.EF.Models.Safra.Funnel;
using TelaUnica.Infra.Data.EF.Models.Stellantis;
using TelaUnica.Infra.Data.EF.Models.Support;
using TelaUnica.Infra.Data.EF.Models.TelaUnica.CalculationsParameters;

namespace TelaUnica.Infra.Data.EF.Data;

public class DataContext(DbContextOptions<DataContext> options) : DbContext(options)
{
    private static readonly CultureInfo ptBrCultureInfo = CultureInfo.GetCultureInfo("pt-BR");
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ModuleRole>().HasKey(mr => new { mr.RoleId, mr.ModuleId });
        // modelBuilder.Entity<UserRoles>().HasKey(mr => new { mr.RolesId, mr.UsersId });
        modelBuilder.Entity<UserGroup>().HasKey(mr => new { mr.UserId, mr.GroupId });
        modelBuilder.Entity<ClientGroup>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<User>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<MktzapMessage>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<SubModuleRole>().HasKey(mr => new { mr.RoleId, mr.SubModuleId });
        modelBuilder.Entity<TactiumTypesService>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<TactiumStatusCall>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<TactiumTypesCall>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<DelayRangeNewcon>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<CalculationTemplate>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<CalculationDischargeWithPrior>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<CalculationPaymentInInstallments>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<CalculationMainDiscount>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<CalculationCashSettlement>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<CalculationInstallmentRegularization>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<DownloadDocProjuris>().HasKey(mr => new { mr.Id, mr.UserId });
        modelBuilder.Entity<WebsocketConnections>().HasKey(mr => new { mr.Id, mr.UserId });
        modelBuilder.Entity<UserDatacobAccess>().HasKey(mr => new { mr.UserId, mr.DatacobAccessId });
        modelBuilder.Entity<GroupCalculationDatacob>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<TactiumToken>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<NewconAPI>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<NewconAccess>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<DatacobInvoices>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<Service>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<DatacobPhoneCall>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<DatacobOccurrence>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<MktzapCompany>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<TicketBTG>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<CodParamBTG>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<UserCrm>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<BtgAttemptBatch>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<BtgAttemptBatchContracts>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<DatacobSaveNegotiation>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<TUNegociacaoCalculoLivre>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<DatacobJokerTicket>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<LogApi>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<BbcAuth>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<Notification>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<BbcProducts>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<BbcConfigs>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<BbcInstallmentParam>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<BbcDiscountParam>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<SafraCampaignPermissions>().HasKey(mr => new { mr.Id });

        modelBuilder.Entity<RpaOccurrenceTriggers>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<TransferCampaign>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<CustomerHistory>().HasKey(mr => new { mr.Id });

        /* Campanhas Safra */
        modelBuilder.Entity<SafraCampaign>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<SafraCampaignItem>().HasKey(mr => new { mr.Id });

        /* Stellantis */
        modelBuilder.Entity<StellantisAuth>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<StellantisAuth>().HasQueryFilter(m => m.DeletedAt == null);

        modelBuilder.Entity<Config>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<Group>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<ClientGroup>().HasQueryFilter(m => m.Group.DeletedAt == null);
        modelBuilder.Entity<Module>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<SubModule>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<ModuleRole>().HasQueryFilter(m => m.Role.DeletedAt == null && m.Module.DeletedAt == null);
        modelBuilder.Entity<SubModuleRole>().HasQueryFilter(m => m.Role.DeletedAt == null && m.SubModule.Module.DeletedAt == null && m.SubModule.DeletedAt == null);
        modelBuilder.Entity<Role>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<User>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<UserGroup>().HasQueryFilter(m => m.User.DeletedAt == null && m.Group.DeletedAt == null);
        modelBuilder.Entity<DelayRangeNewcon>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<DownloadDocProjuris>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<WebsocketConnections>().HasQueryFilter(m => m.User.DeletedAt == null);
        modelBuilder.Entity<UserDatacobAccess>().HasQueryFilter(m => m.User.DeletedAt == null && m.DatacobAccess.DeletedAt == null);
        modelBuilder.Entity<GroupCalculationDatacob>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<NewconAPI>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<NewconAccess>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<Service>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<MktzapCompany>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<TicketBTG>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<CodParamBTG>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<UserCrm>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<BtgAttemptBatch>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<BtgAttemptBatchContracts>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<DatacobSaveNegotiation>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<TUNegociacaoCalculoLivre>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<LogApi>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<SafraCampaignPermissions>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<BbcAuth>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<Notification>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<BbcProducts>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<BbcConfigs>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<BbcDiscountParam>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<UserControlTicket>().HasKey(mr => new { mr.Id });

        // Tabelas Telefonia
        modelBuilder.Entity<Telephony>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<AgentFone>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<AgentCall>().HasKey(mr => new { mr.Id, mr.AgentId });
        modelBuilder.Entity<AgentFonePause>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<PauseMotive>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<CallCampaign>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<CallCampaign>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<AgentCampaign>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<PauseTactium>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<MissedOccurrence>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<CorJuridicoParam>().HasKey(mr => new { mr.Id });

        modelBuilder.Entity<CalculationAdditionEntrySimulator>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<CalculationDilutionEntrySimulator>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<AdditionEntrySimulatorDelayParam>().HasKey(mr => new { mr.Id });

        modelBuilder.Entity<TradingRestrictionModel>().HasKey(p => p.Id);
        modelBuilder.Entity<TradingRestrictionModel>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<RestrictionAnalysisRequestModel>().HasKey(p => p.Id);
        modelBuilder.Entity<RestrictionAnalysisRequestModel>().HasQueryFilter(m => m.DeletedAt == null);

        /*Acordos baseados no Safra*/
        modelBuilder.Entity<FinancedModel>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<ContractModel>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<ContractInstalmentModel>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<AgreementModel>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<AgreementInstallmentModel>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<AgreementTicketModel>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<InvoiceModel>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<FunnelSafraModel>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<PauseTactium>().HasQueryFilter(m => m.DeletedAt == null);

        /* Campanhas Safra */
        modelBuilder.Entity<SafraCampaign>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<SafraCampaignItem>().HasQueryFilter(m => m.DeletedAt == null);

        /*BBC*/
        modelBuilder.Entity<Agreement>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<AgreementSimulation>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<AgreementInstallment>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<AgreementSimulationInstallment>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<AgreementSimulationInstallmentInput>().HasQueryFilter(m => m.DeletedAt == null);

        modelBuilder.Entity<RpaOccurrenceTriggers>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<TransferCampaign>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<MissedOccurrence>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<CorJuridicoParam>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<BbcInstallmentParam>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<UserControlTicket>().HasQueryFilter(m => m.DeletedAt == null);

        modelBuilder.Entity<CalculationAdditionEntrySimulator>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<CalculationDilutionEntrySimulator>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<AdditionEntrySimulatorDelayParam>().HasQueryFilter(m => m.DeletedAt == null);

        /* OccurrenceRules */
        modelBuilder.Entity<OccurrenceRules>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<OccurrenceRules>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<OccurrenceRulesEmails>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<OccurrenceRulesEmails>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<OccurrenceRulesCustomFields>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<OccurrenceRulesCustomFields>().HasQueryFilter(m => m.DeletedAt == null);


        modelBuilder.Entity<DatacobInvoicesDataNegotiation>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<DatacobInvoicesDataNegotiation>().HasQueryFilter(m => m.DeletedAt == null);

        /* Prestadores Servicos */
        modelBuilder.Entity<PrestadoresServicos>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<PrestadoresServicos>().HasQueryFilter(m => m.DeletedAt == null);

        /*Support*/
        modelBuilder.Entity<DepartmentModel>().HasQueryFilter(m => m.DeletedAt == null);
        DateTime date = DateTime.Parse("2023-08-04T14:49:00");

        /*AcordoManual*/
        modelBuilder.Entity<AcordoManual>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<AcordoManual>().HasQueryFilter(m => m.DeletedAt == null);

        modelBuilder.Entity<AcordoManualParcela>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<AcordoManualParcela>().HasQueryFilter(m => m.DeletedAt == null);

        /* Cartas e Termos */
        modelBuilder.Entity<TipoCartaETermo>()
            .HasKey(mr => new { mr.Id });
        modelBuilder.Entity<TipoCartaETermo>()
            .Property(p => p.Id)
            .ValueGeneratedOnAdd();

        modelBuilder.Entity<PedidoCartasETermos>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<PedidoCartasETermos>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<PedidoTermoInfos>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<PedidoTermoInfos>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<PedidoTermoParcelas>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<PedidoTermoParcelas>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<ConteudoTermo>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<ConteudoTermo>().HasQueryFilter(m => m.DeletedAt == null);
        modelBuilder.Entity<TipoTermo>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<TipoTermo>().HasQueryFilter(m => m.DeletedAt == null);

        modelBuilder.Entity<IntegrationConfiguration>().HasQueryFilter(m => m.DeletedAt == null);

        modelBuilder.Entity<DatacobPixHistory>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<DatacobPixHistory>().HasQueryFilter(m => m.DeletedAt == null);

        modelBuilder.Entity<DatacobInstallmentInsertion>().HasKey(mr => new { mr.Id });
        modelBuilder.Entity<DatacobInstallmentInsertion>().HasQueryFilter(m => m.DeletedAt == null);


        modelBuilder.Entity<User>().HasData(
            new User
            {
                Id = 1,
                Username = "Admin",
                IsAdmin = true,
                Ad = false,
                Name = "Administrador",
                PassworHash = new byte[] { 17, 139, 87, 158, 254, 96, 35, 66, 220, 172, 147, 246, 164, 58, 203, 186, 247, 171, 181, 30, 195, 41, 192, 64, 127, 147, 190, 247, 152, 213, 111, 52, 151, 168, 15, 112, 189, 202, 34, 4, 51, 94, 30, 56, 116, 60, 19, 57, 135, 3, 98, 165, 168, 70, 151, 126, 164, 242, 9, 182, 244, 174, 249, 192 },
                PassworSalt = new byte[] { 82, 236, 33, 46, 55, 59, 129, 55, 247, 124, 225, 246, 187, 91, 237, 180, 226, 205, 231, 62, 196, 68, 165, 19, 37, 6, 83, 206, 72, 247, 175, 104, 142, 39, 110, 42, 131, 147, 125, 143, 251, 230, 62, 65, 133, 195, 142, 50, 65, 121, 24, 109, 8, 180, 31, 241, 176, 133, 147, 175, 242, 17, 242, 146, 100, 27, 181, 211, 169, 147, 117, 149, 198, 158, 0, 1, 179, 77, 32, 244, 16, 1, 14, 45, 105, 79, 97, 143, 155, 192, 193, 214, 39, 31, 157, 22, 23, 86, 65, 251, 170, 168, 62, 57, 106, 94, 244, 221, 144, 0, 175, 4, 36, 4, 244, 70, 196, 231, 250, 250, 38, 152, 190, 119, 174, 210, 90, 120 },
                CreatedUserId = 1,
                UpdatedUserId = 1,
                CreatedAt = date,
                UpdatedAt = date
            }
        );

        modelBuilder.Entity<User>()
            .Property(u => u.AutomaticReconnectionOlos)
            .HasDefaultValue(true);

        modelBuilder.Entity<User>().HasData(
            new User
            {
                Id = 2,
                Username = "tela_unica_hangfire",
                IsAdmin = true,
                Ad = false,
                Name = "Tela Unica HangFire",
                PassworHash = [17, 139, 87, 158, 254, 96, 35, 66, 220, 172, 147, 246, 164, 58, 203, 186, 247, 171, 181, 30, 195, 41, 192, 64, 127, 147, 190, 247, 152, 213, 111, 52, 151, 168, 15, 112, 189, 202, 34, 4, 51, 94, 30, 56, 116, 60, 19, 57, 135, 3, 98, 165, 168, 70, 151, 126, 164, 242, 9, 182, 244, 174, 249, 192],
                PassworSalt = [82, 236, 33, 46, 55, 59, 129, 55, 247, 124, 225, 246, 187, 91, 237, 180, 226, 205, 231, 62, 196, 68, 165, 19, 37, 6, 83, 206, 72, 247, 175, 104, 142, 39, 110, 42, 131, 147, 125, 143, 251, 230, 62, 65, 133, 195, 142, 50, 65, 121, 24, 109, 8, 180, 31, 241, 176, 133, 147, 175, 242, 17, 242, 146, 100, 27, 181, 211, 169, 147, 117, 149, 198, 158, 0, 1, 179, 77, 32, 244, 16, 1, 14, 45, 105, 79, 97, 143, 155, 192, 193, 214, 39, 31, 157, 22, 23, 86, 65, 251, 170, 168, 62, 57, 106, 94, 244, 221, 144, 0, 175, 4, 36, 4, 244, 70, 196, 231, 250, 250, 38, 152, 190, 119, 174, 210, 90, 120],
                CreatedUserId = 1,
                UpdatedUserId = 1,
                CreatedAt = date,
                UpdatedAt = date
            }
        );


        /*Calculations Parameters*/
        modelBuilder.Entity<CustomerDataCobParametersCalculationsModel>().HasQueryFilter(m => m.DeletedAt == null);


        modelBuilder.Entity<Config>().HasData(
            new { Id = new Guid("e4723397-b277-423f-89f8-490bff974212"), Key = "datacob_rodobens_token", Value = "", CreatedAt = DateTime.Parse("2023-08-04"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-04"), },
            new { Id = new Guid("446e7766-09d8-4a0e-9f7a-f3de1776dd65"), Key = "datacob_rodobens_token_expires_at", Value = "", CreatedAt = DateTime.Parse("2023-08-05"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-05"), },
            new { Id = new Guid("a5fa8fd8-2ea9-4875-9379-431412928c77"), Key = "datacob_rodobens_login", Value = "boleto_acordoonline_gvc", CreatedAt = DateTime.Parse("2023-08-06"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-06"), },
            new { Id = new Guid("ab1bbb82-b47a-4299-b51a-ce5c1a39524f"), Key = "datacob_rodobens_password", Value = "AwsX76@ykLmN22", CreatedAt = DateTime.Parse("2023-08-07"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-07"), },
            new { Id = new Guid("95fd188d-104d-4dea-893a-c2fc433f5483"), Key = "datacob_rodobens_apikey", Value = "L9GTjCjbmbI=", CreatedAt = DateTime.Parse("2023-08-08"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-08"), },
            new { Id = new Guid("5afde46c-ccf2-4e18-90c7-41ae5f01d1a2"), Key = "datacob_gvc_token", Value = "", CreatedAt = DateTime.Parse("2023-08-09"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-09"), },
            new { Id = new Guid("f274edd7-0684-4015-a0ca-52b751899401"), Key = "datacob_gvc_token_expires_at", Value = "", CreatedAt = DateTime.Parse("2023-08-10"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-10"), },
            new { Id = new Guid("5fb7da57-e8b8-4724-8918-45fa5333818c"), Key = "datacob_gvc_login", Value = "api.gvc.acordoonline", CreatedAt = DateTime.Parse("2023-08-11"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-11"), },
            new { Id = new Guid("c0654490-fda4-4055-8ec7-f4cab34e6d90"), Key = "datacob_gvc_password", Value = "bMpSXltzSFVppA6HaVXu", CreatedAt = DateTime.Parse("2023-08-12"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-12"), },
            new { Id = new Guid("53b1915b-2383-4258-b82e-3d60e4532d30"), Key = "datacob_gvc_apikey", Value = "", CreatedAt = DateTime.Parse("2023-08-13"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-13"), },
            new { Id = new Guid("e0542d8f-aa1e-48ce-97ec-af0718e9f554"), Key = "datacob_gvc_url", Value = "", CreatedAt = DateTime.Parse("2023-08-14"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-14"), },
            new { Id = new Guid("6cb34126-d8b8-41c8-ae90-11cbb4927c64"), Key = "datacob_rodobens_url", Value = "", CreatedAt = DateTime.Parse("2023-08-15"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-15"), },
            new { Id = new Guid("d47ddee6-cab5-4c4f-a19a-6856d5aa05fe"), Key = "custas_gvc_local_file", Value = "", CreatedAt = DateTime.Parse("2023-08-16"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-16"), },
            new { Id = new Guid("9e329152-d264-4414-93f8-6d4c32e93d87"), Key = "custas_rodobens_local_file", Value = "", CreatedAt = DateTime.Parse("2023-08-17"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-17"), },
            new { Id = new Guid("2762b88b-b6d6-4d74-adf1-c3cf69725ded"), Key = "rpa_datacob_rodobens_login", Value = "", CreatedAt = DateTime.Parse("2023-08-18"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-18"), },
            new { Id = new Guid("dcfc8692-1b15-469f-bd4b-226f9ccb432a"), Key = "rpa_datacob_rodobens_password", Value = "", CreatedAt = DateTime.Parse("2023-08-19"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-19"), },
            new { Id = new Guid("87a8c327-ce2b-407f-90b6-d5f59564b21c"), Key = "rpa_datacob_gvc_login", Value = "", CreatedAt = DateTime.Parse("2023-08-20"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-20"), },
            new { Id = new Guid("f50ca345-8471-4f6c-a440-67a0dbaefc79"), Key = "rpa_datacob_gvc_password", Value = "", CreatedAt = DateTime.Parse("2023-08-21"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-21"), },
            new { Id = new Guid("19248a4e-00d2-4b28-a88a-4c5a20efc97f"), Key = "rpa_datacob_rodobens_url", Value = "https://hml-cobranca.gvcsolucoes.com.br/", CreatedAt = DateTime.Parse("2023-08-22"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-22"), },
            new { Id = new Guid("a164e3e2-7250-47fb-abb6-e53926c9063f"), Key = "rpa_datacob_gvc_url", Value = "https://hml-cobranca.gvcsolucoes.com.br/", CreatedAt = DateTime.Parse("2023-08-23"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-23"), },
            new { Id = new Guid("6c566529-e5bd-44fb-9eb1-ec6ef5848f56"), Key = "rpa_custas_active", Value = "0", CreatedAt = DateTime.Parse("2023-08-24"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-24"), },
            new { Id = new Guid("2f54931f-dca2-49dc-938e-8367fe52e9df"), Key = "rpa_custas_dir", Value = "", CreatedAt = DateTime.Parse("2023-08-25"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-25"), },
            new { Id = new Guid("fc5622b7-8c05-4596-a42b-cf70ba03c150"), Key = "rpa_custas_on_off", Value = "1", CreatedAt = DateTime.Parse("2023-08-26"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-26"), },
            new { Id = new Guid("6a1a4c5b-5352-4ee7-9c11-eb6e80d24092"), Key = "mktzap_url", Value = "https://api.mktzap.com.br/company/", CreatedAt = DateTime.Parse("2023-08-27"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-27"), },
            new { Id = new Guid("1d1c1f76-a6a2-4575-996f-7e7eae2be0b0"), Key = "mktzap_company", Value = "196", CreatedAt = DateTime.Parse("2023-08-28"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-28"), },
            new { Id = new Guid("00ecacea-cc3e-414c-a75b-a7751573e87a"), Key = "mktzap_clientkey", Value = "e3e69a81bd8c462eae11d192bb803240", CreatedAt = DateTime.Parse("2023-08-29"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-29"), },
            new { Id = new Guid("f252f12f-1e82-4373-943a-62b4aa51404e"), Key = "mktzap_token", Value = "", CreatedAt = DateTime.Parse("2023-08-30"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-30"), },
            new { Id = new Guid("7a1f9e34-baed-454b-9368-24218ed446d9"), Key = "mktzap_token_expires", Value = "", CreatedAt = DateTime.Parse("2023-08-31"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-08-31"), },
            new { Id = new Guid("fbaef06c-26cc-4e48-970f-9d0bcf036ae1"), Key = "newcon_url", Value = "https://www03.rodobens.com.br", CreatedAt = DateTime.Parse("2023-09-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-01"), },
            new { Id = new Guid("038cbfa2-fd8d-47e2-b5cc-c838ab6c3fe0"), Key = "tactium_url", Value = "", CreatedAt = DateTime.Parse("2023-09-02"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-02"), },
            new { Id = new Guid("40c3f545-81b1-4c2c-b73b-36a0803549c0"), Key = "tactium_token", Value = "", CreatedAt = DateTime.Parse("2023-09-03"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-03"), },
            new { Id = new Guid("b0354664-6416-412d-b6b9-10b5b7463632"), Key = "tactium_token_expires_at", Value = "", CreatedAt = DateTime.Parse("2023-09-04"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-04"), },
            new { Id = new Guid("39bb5746-cdaf-4a1b-8e75-66ccfe2c0a01"), Key = "tactium_login", Value = "tela_unica", CreatedAt = DateTime.Parse("2023-09-05"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-05"), },
            new { Id = new Guid("ac71cc20-9fa8-4fd3-ad6e-bbba375f2334"), Key = "tactium_password", Value = "teste@123", CreatedAt = DateTime.Parse("2023-09-06"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-06"), },
            new { Id = new Guid("255459d1-2350-41c9-befa-5a55b2ae8a1a"), Key = "tactium_webhook", Value = "/api/Tactium/Webhook", CreatedAt = DateTime.Parse("2023-09-07"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-07"), },
            new { Id = new Guid("94314677-3cbe-4f21-a2a8-7224017de93f"), Key = "websocket_hml", Value = "", CreatedAt = DateTime.Parse("2023-09-08"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-08"), },
            new { Id = new Guid("a7484542-1f11-453b-96c8-af037d89ea6c"), Key = "websocket_dev", Value = "", CreatedAt = DateTime.Parse("2023-09-09"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-09"), },
            new { Id = new Guid("0b0a186d-7a81-4e84-a794-e714eba1d342"), Key = "websocket_prd", Value = "", CreatedAt = DateTime.Parse("2023-09-10"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-10"), },
            new { Id = new Guid("fa7ba3b3-1d13-4aec-9657-46a50b0d6d12"), Key = "projuris_doc_path_hml", Value = "C:\\Users\\<USER>\\Downloads\\telas", CreatedAt = DateTime.Parse("2023-09-11"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-11"), },
            new { Id = new Guid("0d230934-0a49-4616-b2a6-c0a92e51b320"), Key = "projuris_doc_path_dev", Value = "C:\\Users\\<USER>\\Downloads\\telas", CreatedAt = DateTime.Parse("2023-09-12"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-12"), },
            new { Id = new Guid("063b95e7-1974-4df6-a42a-a6d392d0442e"), Key = "projuris_doc_path_prd", Value = "C:\\Users\\<USER>\\Downloads\\telas", CreatedAt = DateTime.Parse("2023-09-13"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-13"), },
            new { Id = new Guid("52c60e31-4af2-4a51-831f-cd4caecd53d1"), Key = "manager_url", Value = "", CreatedAt = DateTime.Parse("2023-09-14"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-14"), },
            new { Id = new Guid("b19c2bc5-f286-4f95-a04e-be7de93afe52"), Key = "manager_email", Value = "", CreatedAt = DateTime.Parse("2023-09-15"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-15"), },
            new { Id = new Guid("1aaccaa2-07a6-4c86-8bf0-a807cf8cc1ed"), Key = "manager_password", Value = "", CreatedAt = DateTime.Parse("2023-09-16"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-16"), },
            new { Id = new Guid("b8a5ae3e-e9b0-42f9-9ca5-9a8fb68b9522"), Key = "manager_token", Value = "", CreatedAt = DateTime.Parse("2023-09-17"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-17"), },
            new { Id = new Guid("c110a859-e385-44d6-8a84-2d4d25221eb1"), Key = "manager_token_expires_at", Value = "", CreatedAt = DateTime.Parse("2023-09-18"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-18"), },
            new { Id = new Guid("6bb68404-80fe-44ea-817d-06f850be96c4"), Key = "datacob_id_ocorrencia_historico", Value = "[]", CreatedAt = DateTime.Parse("2023-09-19"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-19"), },
            new { Id = new Guid("65e89aa3-382f-4e03-adff-75663b7d7137"), Key = "barramento_safra_cadastar_acordo", Value = "", CreatedAt = DateTime.Parse("2023-09-20"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-20"), },
            new { Id = new Guid("fddd658b-bcb1-46f2-8c67-9d5a58f071b9"), Key = "barramento_safra_cancelar_acordo", Value = "", CreatedAt = DateTime.Parse("2023-09-21"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-21"), },
            new { Id = new Guid("e99c091e-4c62-4248-86d0-82d0586d91c8"), Key = "barramento_safra_consulta_elegibilidade", Value = "", CreatedAt = DateTime.Parse("2023-09-22"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-22"), },
            new { Id = new Guid("8a86eba3-c779-4fc3-aef0-************"), Key = "barramento_safra_consulta_saldo", Value = "", CreatedAt = DateTime.Parse("2023-09-23"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-23"), },
            new { Id = new Guid("1a1c6356-c804-42bc-9106-d57eb8d0806e"), Key = "barramento_safra_simular_acordo", Value = "", CreatedAt = DateTime.Parse("2023-09-24"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-24"), },
            new { Id = new Guid("ea00a66e-a105-4c20-8ce3-7ac77da3e052"), Key = "barramento_safra_autenticar", Value = "", CreatedAt = DateTime.Parse("2023-09-25"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-25"), },
            new { Id = new Guid("9a6a4b94-756f-4fa5-8ef1-d4838d723e4a"), Key = "barramento_safra_gerar_boleto_parcela", Value = "", CreatedAt = DateTime.Parse("2023-09-26"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-26"), },
            new { Id = new Guid("92b9f3ee-6b96-41a5-bcda-86dea2da1a78"), Key = "barramento_safra_buscar_boleto_parcela", Value = "", CreatedAt = DateTime.Parse("2023-09-27"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-27"), },
            new { Id = new Guid("b4f4d04b-e762-45e1-8cc9-6d3cfdc151f5"), Key = "barramento_safra_enviar_email", Value = "", CreatedAt = DateTime.Parse("2023-09-28"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-28"), },
            new { Id = new Guid("415446e4-0451-405e-a7f0-90137b46e729"), Key = "grupos_datacob_gvc_cyber_safra", Value = "[\"4\"]", CreatedAt = DateTime.Parse("2023-09-29"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-29"), },
            new { Id = new Guid("df0979d4-0512-4ecf-b5d3-dd9aea883d3a"), Key = "grupos_datacob_rodobens_cyber_safra", Value = "[\"53\"]", CreatedAt = DateTime.Parse("2023-09-30"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-30"), },
            new { Id = new Guid("ad7c1f50-7b1a-4ae3-b018-a1a20e7ecc4d"), Key = "olos_url", Value = "", CreatedAt = DateTime.Parse("2023-10-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-01"), },
            new { Id = new Guid("e483212c-51d3-436b-8de1-fa2b26352ccb"), Key = "olos_username", Value = "", CreatedAt = DateTime.Parse("2023-10-02"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-02"), },
            new { Id = new Guid("5ebe4634-e71e-4850-899a-e69eb018b2a9"), Key = "olos_password", Value = "", CreatedAt = DateTime.Parse("2023-10-03"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-03"), },
            new { Id = new Guid("b2141c88-7b6a-4f00-8846-08cc779818e6"), Key = "olos_grand_type", Value = "password", CreatedAt = DateTime.Parse("2023-10-04"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-04"), },
            new { Id = new Guid("659afb0d-30a3-44fe-bd02-4a5b7e951618"), Key = "olos_client_id", Value = "", CreatedAt = DateTime.Parse("2023-10-05"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-05"), },
            new { Id = new Guid("3dd141f3-b42a-4a35-838d-103f7853cad4"), Key = "olos_secret", Value = "", CreatedAt = DateTime.Parse("2023-10-06"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-06"), },
            new { Id = new Guid("e0f7ad6e-7522-49b6-af7b-94181d600357"), Key = "olos_token", Value = "", CreatedAt = DateTime.Parse("2023-10-07"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-07"), },
            new { Id = new Guid("1ca64bea-dcce-453d-8c11-a0e90b2ee8cb"), Key = "olus_expires_in", Value = "", CreatedAt = DateTime.Parse("2023-10-08"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-08"), },
            new { Id = new Guid("4554724a-444f-434a-9c43-57e1fe77c26d"), Key = "newcon_atraso_api", Value = "0", CreatedAt = DateTime.Parse("2023-10-09"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-09"), },
            new { Id = new Guid("49ef2433-64c8-4c6e-998f-ad009cba3a05"), Key = "newcon_user_id", Value = "-9", CreatedAt = DateTime.Parse("2023-10-10"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-10"), },
            new { Id = new Guid("26819087-0bb8-4691-8418-a3cc96e25dfa"), Key = "newcon_api_token", Value = "teste", CreatedAt = DateTime.Parse("2023-10-11"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-11"), },
            new { Id = new Guid("0179b28c-a849-4747-b9ea-a0884059e077"), Key = "smtp_from_support", Value = "", CreatedAt = DateTime.Parse("2023-10-12"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-12"), },
            new { Id = new Guid("bd8d373c-1eaa-48c6-a11f-3473647b3e93"), Key = "smtp_email_user_destinario_support", Value = "", CreatedAt = DateTime.Parse("2023-10-13"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-13"), },
            new { Id = new Guid("d9b9ee02-4251-4a53-8759-2b0768b57c89"), Key = "smtp_server_support", Value = "", CreatedAt = DateTime.Parse("2023-10-14"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-14"), },
            new { Id = new Guid("18d1d2c7-c9b1-4833-8a83-ae4a38fcaddc"), Key = "smtp_user_support", Value = "apikey", CreatedAt = DateTime.Parse("2023-10-15"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-15"), },
            new { Id = new Guid("41b70206-84dc-4b47-86a8-52aeb1553100"), Key = "smtp_password_support", Value = "", CreatedAt = DateTime.Parse("2023-10-16"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-16"), },
            new { Id = new Guid("fb4ee8f8-6b06-4264-beab-4fafb7ca3b45"), Key = "smtp_port_support", Value = "587", CreatedAt = DateTime.Parse("2023-10-17"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-17"), },
            new { Id = new Guid("f9bda6a2-8cd2-4bd9-b5d5-ec43393229ca"), Key = "smtp_enable_ssl_support", Value = "true", CreatedAt = DateTime.Parse("2023-10-18"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-18"), },
            new { Id = new Guid("49377cba-0b85-48d4-ad12-9798d5b08669"), Key = "newcon_cota_api", Value = "0", CreatedAt = DateTime.Parse("2023-10-19"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-19"), },
            new { Id = new Guid("b9b57229-2c9a-488b-822e-f42407bee70f"), Key = "url_barramento", Value = "", CreatedAt = DateTime.Parse("2023-10-20"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-20"), },
            new { Id = new Guid("3bf2f73f-ef1b-4b72-8304-a7d502f6987f"), Key = "mktzap_dias_busca", Value = "60", CreatedAt = DateTime.Parse("2023-10-21"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-21"), },
            new { Id = new Guid("ccb5973f-87c4-4de0-a39b-c94d5f24ffa4"), Key = "smtp_from_ticket", Value = "", CreatedAt = DateTime.Parse("2023-10-22"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-22"), },
            new { Id = new Guid("c46b8cd1-c303-4f97-88aa-73e3c9cca6a7"), Key = "smtp_server_ticket", Value = "", CreatedAt = DateTime.Parse("2023-10-23"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-23"), },
            new { Id = new Guid("8ec314f0-aa9d-4ab6-ac1b-e34d12e8243e"), Key = "smtp_user_ticket", Value = "apikey", CreatedAt = DateTime.Parse("2023-10-24"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-24"), },
            new { Id = new Guid("dc8ad848-71b9-4240-b46e-70e84053a1b9"), Key = "smtp_password_ticket", Value = "", CreatedAt = DateTime.Parse("2023-10-25"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-25"), },
            new { Id = new Guid("34a7dff9-3d01-4b1e-a612-9c659a9f0881"), Key = "smtp_port_ticket", Value = "587", CreatedAt = DateTime.Parse("2023-10-26"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-26"), },
            new { Id = new Guid("69a3c980-f4f8-43c4-be44-db5b44744fcd"), Key = "smtp_enable_ssl_ticket", Value = "true", CreatedAt = DateTime.Parse("2023-10-27"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-27"), },
            new { Id = new Guid("4af96968-7cfc-4334-abac-7dc781e2bb6b"), Key = "ticket_email_password_number", Value = "6", CreatedAt = DateTime.Parse("2023-10-28"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-28"), },
            new { Id = new Guid("4eaf405a-94c8-432c-bef7-253871a17984"), Key = "rpa_save_occurrences_active", Value = "0", CreatedAt = DateTime.Parse("2023-10-29"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-29"), },
            new { Id = new Guid("8f6502a3-ad10-4bf5-9008-b1e3e3448da7"), Key = "ad_context", Value = "Domain", CreatedAt = DateTime.Parse("2023-10-30"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-30"), },
            new { Id = new Guid("9f498f0e-6deb-41a1-b72a-9535640cb8a9"), Key = "ad_values", Value = "[]", CreatedAt = DateTime.Parse("2023-10-31"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-10-31"), },
            new { Id = new Guid("1c30b539-4170-40ec-b9ca-3eea4b6e8b3d"), Key = "olos_std_transfer_campaign", Value = "114", CreatedAt = DateTime.Parse("2023-11-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-01"), },
            new { Id = new Guid("8c3656bf-a678-4667-83e0-9fc5424a3f0b"), Key = "ocorrencia_sem_fone", Value = "[]", CreatedAt = DateTime.Parse("2023-11-02"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-02"), },
            new { Id = new Guid("a53e44a7-6dec-449b-8c15-3a74ca26f493"), Key = "grupos_forma_livre", Value = "{\"rodobens\":[], \"gvc\":[]}", CreatedAt = DateTime.Parse("2023-11-03"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-03"), },
            new { Id = new Guid("a6b36957-5463-48b6-9303-a26225caad7b"), Key = "param_forma_livre", Value = "1", CreatedAt = DateTime.Parse("2023-11-04"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-04"), },
            new { Id = new Guid("546229b1-7af1-4361-9fad-1c7d86391c7e"), Key = "btg_url", Value = "", CreatedAt = DateTime.Parse("2023-11-05"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-05"), },
            new { Id = new Guid("*************-4558-9561-fc00ec31e5e8"), Key = "btg_token", Value = "", CreatedAt = DateTime.Parse("2023-11-06"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-06"), },
            new { Id = new Guid("bbf7dabb-1b76-4d42-b90b-c6561a4938e2"), Key = "btg_expires_in", Value = "", CreatedAt = DateTime.Parse("2023-11-07"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-07"), },
            new { Id = new Guid("1cb8288d-0b33-458c-ae71-d7d02f69a4a1"), Key = "btg_grupos", Value = "{\"rodobens\":[], \"gvc\":[]}", CreatedAt = DateTime.Parse("2023-11-08"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-08"), },
            new { Id = new Guid("4c1fbc5c-756d-4b55-8c6e-97b2a20a9158"), Key = "aws_s3_accesskey", Value = "", CreatedAt = DateTime.Parse("2023-11-09"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-09"), },
            new { Id = new Guid("e44e82c2-760d-469e-90d8-31b6e8ad5ed7"), Key = "aws_s3_secretekey", Value = "", CreatedAt = DateTime.Parse("2023-11-10"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-10"), },
            new { Id = new Guid("61e0f416-b044-410b-a18e-c00353557716"), Key = "use_aws_athena_s3", Value = "true", CreatedAt = DateTime.Parse("2023-11-11"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-11"), },
            new { Id = new Guid("ac0dcff9-a86d-4951-8c71-a1cf185c64b1"), Key = "permissao_desconto", Value = "{\"rodobens\":[], \"gvc\":[]}", CreatedAt = DateTime.Parse("2023-11-12"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-12"), },
            new { Id = new Guid("06e63153-b821-4800-b591-355bdb50bd67"), Key = "liberar_calculo_manual", Value = "[]", CreatedAt = DateTime.Parse("2023-11-13"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-13"), },
            new { Id = new Guid("30a2a135-25f6-4979-9f78-1cd8163e7a42"), Key = "aprovacao_juridica", Value = "[\"Admin\"]", CreatedAt = DateTime.Parse("2023-11-14"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-14"), },
            new { Id = new Guid("b9f3949b-092d-4e8b-8dae-5985789b1e35"), Key = "fases_aprovacao_juridica", Value = "[\"\"]", CreatedAt = DateTime.Parse("2023-11-15"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-15"), },
            new { Id = new Guid("bce6b61f-a62e-4c26-ae0c-3bc66ad5570f"), Key = "olos_dispostion_id", Value = "[1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]", CreatedAt = DateTime.Parse("2023-11-16"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-16"), },
            new { Id = new Guid("e2940342-a470-4e85-af65-d1c100b90389"), Key = "olos_btg_campanha", Value = "[62,64]", CreatedAt = DateTime.Parse("2023-11-17"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-17"), },
            new { Id = new Guid("2718f80b-b2a4-49ae-a117-ee211cd43a78"), Key = "funcoes_supervisoras", Value = "[]", CreatedAt = DateTime.Parse("2023-11-18"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-18"), },
            new { Id = new Guid("ea44e10b-081f-4950-9282-3956c466146f"), Key = "LogApi", Value = "0", CreatedAt = DateTime.Parse("2023-11-19"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-19"), },
            new { Id = new Guid("4ef00084-41e5-4e0c-97d5-a1d7b4b9b55d"), Key = "projuris_token", Value = "", CreatedAt = DateTime.Parse("2023-11-20"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-20"), },
            new { Id = new Guid("ede24564-96a6-42ad-880b-53d04a3a14db"), Key = "projuris_expires_at", Value = "", CreatedAt = DateTime.Parse("2023-11-21"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-21"), },
            new { Id = new Guid("9fd73656-5cbb-4b2c-bd4f-af37b1ec919b"), Key = "projuris_url", Value = "", CreatedAt = DateTime.Parse("2023-11-21"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-21"), },
            new { Id = new Guid("527e478a-088d-424b-9b4c-cd3b6def748a"), Key = "users_enable_manual_calc_negotiation", Value = "[]", CreatedAt = DateTime.Parse("2023-11-22"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-11-22"), },
            new { Id = new Guid("325d7e19-6eaf-48a4-9186-2517b736a28f"), Key = "grupos_criacao_acordo", Value = "[]", Obs = "Controle de acesso da tela de criação de acordos por grupos(Id) do Tela Unica", CreatedAt = DateTime.Parse("2024-05-07"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-07-05"), },
            new { Id = new Guid("d8898944-88c7-40c1-a7a2-6f21d954c478"), Key = "bbc_url", Value = "", Obs = "Url para as apis BBC", CreatedAt = DateTime.Parse("2024-05-11"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-05-11"), },
            new { Id = new Guid("97e05876-3fa7-4a33-95eb-5623a8d8cb99"), Key = "grupos_randon_iframe", Value = "[]", Obs = "Controle de acesso da tela do Newcon da Randon por grupos(Id) do Tela Unica", CreatedAt = DateTime.Parse("2024-07-31"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-07-31"), },
            new { Id = new Guid("a40d996e-1e01-4769-98f6-63ba19923f4c"), Key = "randon_newcon_iframe", Value = "", Obs = "Url do Iframe do Newcon da Randon", CreatedAt = DateTime.Parse("2024-07-31"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-07-31"), },
            new { Id = new Guid("0186d1b9-7376-485f-8f59-9055b9763231"), Key = "occr_automatica", Value = "021", Obs = "Ocorrência automática para DNR ou contrato devolvidos/encerrados", CreatedAt = DateTime.Parse("2024-08-06"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-08-06"), },
            new { Id = new Guid("195256f4-1655-4ba8-9f87-337a42bdba2e"), Key = "grupos_calculadora_rni", Value = "{\"rodobens\":[], \"gvc\":[]}", CreatedAt = DateTime.Parse("2024-08-13"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-08-13"), },
            new { Id = new Guid("34b18a0d-11c7-42f4-b43a-f464c5e7213c"), Key = "percetual_valor_entrada_calculadora_rni", Value = "15", CreatedAt = DateTime.Parse("2024-08-13"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-08-13"), },
            new { Id = new Guid("a5deedb8-cd32-4d8c-bde2-ec22ec2390c3"), Key = "dias_apos_venc_acordo", Value = "{\"rodobens\":5, \"gvc\":5}", CreatedAt = DateTime.Parse("2024-09-10"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-09-10"), },
            new { Id = new Guid("21a649dd-06ed-4be4-b115-11e68b2edbcb"), Key = "ura_campaign_olos", Value = "[66,67,68,69,70,71,93,94,95,155,162,163,164,165,166]", Obs = "Ids de campanhas OLOS indicando ligação URA", CreatedAt = DateTime.Parse("2024-10-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-10-01"), },
            new { Id = new Guid("e1f21c97-2016-410d-809d-378fb619d4dd"), Key = "ocorrencia_boleto_grupo", Value = "[]", Obs = "Ids dos grupos tela unica que iram fazer o disparo automático da ocorrência do boleto", CreatedAt = DateTime.Parse("2024-10-16"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-10-01"), },
            new { Id = new Guid("cf93a410-e48e-4d5b-9925-016628dbd55c"), Key = "safra_heavy_client_ids", Value = "[]", Obs = "Ids de clientes Safra indicando pesados", CreatedAt = DateTime.Parse("2024-10-24"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-10-24"), },
            new { Id = new Guid("9a358eaf-97e4-40a6-be53-ae942e0bcd61"), Key = "safra_light_client_ids", Value = "[]", Obs = "Ids de clientes Safra indicando leves", CreatedAt = DateTime.Parse("2024-10-24"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-10-24"), },

            new { Id = new Guid("79e2985e-c2db-4570-af5b-2933b3a702c9"), Key = "grupos_rodobens_iframe", Value = "[1,35]", Obs = "Controle de acesso da tela do Newcon por grupos(Id) do Tela Unica", CreatedAt = DateTime.Parse("2024-11-26"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-11-26"), },
            new { Id = new Guid("9d40dc33-9cb1-414d-a958-54832bbfe424"), Key = "rodobens_newcon_iframe", Value = "", Obs = "Url do Iframe do Newcon", CreatedAt = DateTime.Parse("2024-11-26"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-11-26"), },
            new { Id = new Guid("a0ddf18e-3e49-43c9-8374-7659ece5ec55"), Key = "transcription_call_users", Value = "[]", Obs = "Usuarios que iram usar a Transcrição da chamada telefonica", CreatedAt = DateTime.Parse("2024-12-19"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-12-19"), },
            new { Id = new Guid("9a86c9ef-f410-4c50-9b0f-82b1df2c3707"), Key = "params_websocket_transcription_call", Value = "{\"types\" : [ \"1\" : \"api-speech-recognition\", \"2\": \"api-google\"], selected : \"1\"}", Obs = "Lista de tipos de api de transcrição de voz em texto. informe a api usada no campo selected do json.", CreatedAt = DateTime.Parse("2024-12-19"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-12-19"), },
            new { Id = new Guid("88ecfbb7-c070-4fbb-bfa9-f5607f61e9db"), Key = "url_websocket_transcription_call", Value = "", Obs = "Usuarios que iram usar a Transcrição da chamada telefonica", CreatedAt = DateTime.Parse("2024-12-19"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-12-19"), },

            new { Id = new Guid("5f49fb9f-3c74-45cf-98ec-ee942946ed51"), Key = "controle_usuario_funcoes", Value = "[]", Obs = "Funções que habilitam a visualização de outros usuários na tela de Controle de Usuário", CreatedAt = DateTime.Parse("2024-10-24"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-10-24"), },
            new { Id = new Guid("b8c33aa5-242e-4813-98b3-13ed15c7c500"), Key = "hangfire_link", Value = "https://api-telaunica.gvcsolucoes.com.br/jobs/", Obs = "Link de acesso do hangfire", CreatedAt = DateTime.Parse("2024-12-31"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-12-31"), },
            new { Id = new Guid("4437075c-3e27-424f-b041-ad45f6f027d8"), Key = "hangfire_menu_permissao", Value = "[1]", Obs = "Usuarios com permissão para acessar o hangfire", CreatedAt = DateTime.Parse("2024-12-31"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-12-31"), },
            new { Id = new Guid("7203f03a-4252-47b9-a094-0ec1e8c81a79"), Key = "emails_ocorrencia_43", Value = "[\"<EMAIL>\", \"<EMAIL>\"]", Obs = "Emails de envio da ocorrencia 43", CreatedAt = DateTime.Parse("2025-01-13"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-01-13"), },
            new { Id = new Guid("da4078d0-4b1a-44ca-91f0-84b07789038f"), Key = "emails_cc_ocorrencia_43", Value = "[\"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\"]", Obs = "Emails de Cópia de envio da ocorrencia 43", CreatedAt = DateTime.Parse("2025-01-13"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-01-13"), },

            new { Id = new Guid("6e99a13e-1ab4-46bf-8966-09677f699ca0"), Key = "controle_usuario_funcao", Value = "[]", Obs = "Liberação de campo usuário da tela de controle por função", CreatedAt = DateTime.Parse("2025-02-05"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-02-05"), },
            new { Id = new Guid("89954d2d-28db-4d77-b3f4-a1a936f4ebf2"), Key = "users_show_input_dono_boleto_controle_usuarios", Value = "[]", Obs = "Mostrar input de Usuario dono do boleto. tela de Controle de Usuários", CreatedAt = DateTime.Parse("2025-03-03"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-03-03"), },

            new { Id = new Guid("8c347b61-92eb-4fd8-bce7-af669f4a0a0d"), Key = "softphone_olos_users", Value = "[]", Obs = "Usuários que iram usar o Softphone direto com a Olos", CreatedAt = DateTime.Parse("2025-04-07"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-04-07"), },
            new { Id = new Guid("301125d1-3cce-496a-ab37-1c2db1b970ed"), Key = "tipos_prestadores_servicos", Value = "[\"Outros\",\"Localizador\",\"Guincho\"]", Obs = "Utilizado na tela Cadastro de Prestadores de Serviço", CreatedAt = DateTime.Parse("2025-04-07"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-04-07"), },

            new { Id = new Guid("07bc0cfe-cefd-421b-b38d-daf5f57d184a"), Key = "exportar_boletos_funcoes", Value = "[]", Obs = "Utilizado na tela Cadastro de Prestadores de Serviço", CreatedAt = DateTime.Parse("2025-04-07"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-04-07"), },

            new { Id = new Guid("fbaef06c-26cc-4e48-970f-1c2db1b970ed"), Key = "newcon_servidor_rpa", Value = "gvc.api.datacob_RODCLD01GVRPA01_002248D2C618", CreatedAt = DateTime.Parse("2023-09-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-01") },


            new { Id = new Guid("2a72ce83-5ddf-4fa8-a729-6a7eeec20275"), Key = "barramento_token", Value = "", Encrypted = true, CreatedAt = DateTime.Parse("2023-09-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-01") },
            new { Id = new Guid("251863a0-6447-47a1-99ca-889ef47c3daf"), Key = "barramento_basic", Value = "", Encrypted = true, CreatedAt = DateTime.Parse("2023-09-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-01") },
            new { Id = new Guid("a6ea753a-5cd6-4639-8f57-afa7af41b613"), Key = "barramento_token_expires_at", Value = "", CreatedAt = DateTime.Parse("2023-09-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-01") },
            new { Id = new Guid("5cb64fa4-4c74-48f0-baf3-d2cc9714fa21"), Key = "barramento_fornecedor", Value = "", CreatedAt = DateTime.Parse("2023-09-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-01") },
            new { Id = new Guid("3fcf3640-6214-4fbc-9c28-179ddd595946"), Key = "barramento_senha", Value = "", CreatedAt = DateTime.Parse("2023-09-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-01") },
            new { Id = new Guid("d1fc51e2-b9db-4b36-be0d-31a7e0f3de1e"), Key = "barramento_url", Value = "", Encrypted = true, CreatedAt = DateTime.Parse("2023-09-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-01") },
            new { Id = new Guid("191bd709-f789-482a-840d-20fe57996559"), Key = "stellantis_url", Value = "", Obs = "Url do Stellantis", CreatedAt = DateTime.Parse("2025-06-16"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-06-16"), },
            new { Id = new Guid("846ac1c0-a985-441a-af4c-3fee2028955b"), Key = "stellantis_username", Value = "", Obs = "Usuário do Stellantis", CreatedAt = DateTime.Parse("2025-06-16"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-06-16"), },
            new { Id = new Guid("f4ccdb0e-351a-487d-91db-cfbd732c4230"), Key = "stellantis_password", Value = "", Obs = "Senha do Stellantis", CreatedAt = DateTime.Parse("2025-06-16"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-06-16"), },
            new { Id = new Guid("a0d94ba7-d15b-4744-b5b1-2a978b8452f9"), Key = "stellantis_login_token", Value = "", Obs = "Token login do Stellantis", CreatedAt = DateTime.Parse("2025-06-16"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-06-16"), },
            new { Id = new Guid("33411be8-a6eb-48a0-b21a-32bfd83e9f80"), Key = "barramento_senha_rod", Value = "", CreatedAt = DateTime.Parse("2023-09-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-01") },
            new { Id = new Guid("b54efb13-b5c7-448e-bb60-6ae2f1545a48"), Key = "barramento_fornecedor_rod", Value = "", CreatedAt = DateTime.Parse("2023-09-01"), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2023-09-01") },

            new { Id = new Guid("7dd53e1c-9ae3-4d98-bc67-da82c158a0b3"), Key = "funcoes_backoffice", Value = "", CreatedAt = new DateTime(2025, 8, 3, 0, 0, 0, 0, DateTimeKind.Local), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = new DateTime(2025, 8, 3, 0, 0, 0, 0, DateTimeKind.Local) },
            new { Id = new Guid("c6fac9e7-da9f-46a1-a31d-e852f5289014"), Key = "funcoes_juridicas", Value = "", CreatedAt = new DateTime(2025, 8, 3, 0, 0, 0, 0, DateTimeKind.Local), CreatedUserId = 1, UpdatedUserId = 1, UpdatedAt = new DateTime(2025, 8, 3, 0, 0, 0, 0, DateTimeKind.Local) }

        );

        modelBuilder.Entity<Module>().HasData(
            new { Id = 1, Name = "Tela Principal", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 2, Name = "Negociação", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 3, Name = "Acordos", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 4, Name = "Avalista/Terceiros", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 5, Name = "Jurídico CRM", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 6, Name = "Histórico e registros", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 7, Name = "Configurações", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 8, Name = "Configurações Tela Única", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 9, Name = "Faixas de Atraso", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 10, Name = "Telefonia", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 11, Name = "Simulações de Cálculos", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 12, Name = "Ajuda", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 13, Name = "Randon", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 14, Name = "Parcelamento BBC", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 15, Name = "Desconto BBC", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 16, Name = "Indicativo Jurídico", CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 17, Name = "Permissão de Campanhas", CreatedUserId = 1, CreatedAt = DateTime.Parse("2024-11-04"), UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-11-04") },
            new { Id = 18, Name = "Controle de Usuários", CreatedUserId = 1, CreatedAt = DateTime.Parse("2024-12-24"), UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-12-24") },
            new { Id = 19, Name = "Simulações", CreatedUserId = 1, CreatedAt = DateTime.Parse("2024-11-04"), UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-11-04") },
            new { Id = 20, Name = "Regras Ocorrências", CreatedUserId = 1, CreatedAt = DateTime.Parse("2025-02-03"), UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-02-03") },
            new { Id = 23, Name = "Aprovação Abaixo da Régua", CreatedUserId = 1, CreatedAt = DateTime.Parse("2025-05-20"), UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-05-20") },
            new { Id = 24, Name = "Cadastro de Prestadores de Serviço", CreatedUserId = 1, CreatedAt = DateTime.Parse("2025-02-03"), UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-02-03") },
            new { Id = 26, Name = "Cartas e Termos", CreatedUserId = 1, CreatedAt = DateTime.Parse("2025-08-01"), UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2025-08-01") }
        );

        modelBuilder.Entity<SubModule>().HasData(
            new { Id = 2, Name = "Financiado", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 3, Name = "Endereço", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 4, Name = "Telefone", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 5, Name = "Email", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 6, Name = "Contratos Ativos", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 7, Name = "Saldo Acumulado", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 8, Name = "Saldo Acumulado - Valores Pagos", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 9, Name = "Saldo Acumulado - Saldo Devedor", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 10, Name = "Saldo Acumulado - Atrasos", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 11, Name = "Plano de Cobrança", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 12, Name = "Plano de Cobrança - Detalhes do Plano", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 13, Name = "Bens", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 14, Name = "Bens - Ver Evolução", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 15, Name = "Bens - Ver Detalhes", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 16, Name = "Bens - Garantia", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 17, Name = "Estrutura Comercial", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 18, Name = "Dados dos Sócios", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 19, Name = "Agenda", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 20, Name = "Consultas e Buscas", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 21, Name = "Consultas e Buscas - Consulta de Valores à Devolver", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 22, Name = "Consultas e Buscas - Debito em Conta", ModuleId = 1, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 23, Name = "Safra", ModuleId = 2, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 24, Name = "Simular", ModuleId = 2, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 25, Name = "Simular - Detalhes do Cálculo", ModuleId = 2, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 26, Name = "Simular - Parametros do Cálculo", ModuleId = 2, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 27, Name = "Simular - Emitir Boleto", ModuleId = 2, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 28, Name = "Boleto", ModuleId = 2, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 29, Name = "Boleto - Detalhes do Boleto", ModuleId = 2, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 30, Name = "Custas", ModuleId = 5, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 31, Name = "Jurídico", ModuleId = 5, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 32, Name = "Adicionar ocorrências", ModuleId = 6, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 33, Name = "Gerenciar Usuários", ModuleId = 7, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 34, Name = "Grupos e Clientes", ModuleId = 7, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 35, Name = "Funções", ModuleId = 7, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 36, Name = "Campanhas de Ligação", ModuleId = 10, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 37, Name = "Pausas Tactium", ModuleId = 10, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 38, Name = "MKTZAP", ModuleId = 6, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 39, Name = "Criar Acordos", ModuleId = 3, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 40, Name = "Visualizar Acordos", ModuleId = 3, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 41, Name = "Newcon", ModuleId = 13, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 42, Name = "BBC", ModuleId = 2, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 43, Name = "Atraso Aditamento", ModuleId = 19, CreatedUserId = 1, CreatedAt = DateTime.Parse("2025-01-20"), UpdatedUserId = 1, UpdatedAt = DateTime.Parse("2024-01-20") },
            new { Id = 46, Name = "Logs Integrações", ModuleId = 7, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 50, Name = "Criar Acordos Manuais", ModuleId = 3, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 51, Name = "Cadastro de Prestadores de Serviço", ModuleId = 24, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 52, Name = "Stellantis", ModuleId = 2, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 53, Name = "Pix", ModuleId = 2, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 54, Name = "Buscar Parcelas", ModuleId = 2, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 55, Name = "Fila de Aprovação", ModuleId = 26, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date },
            new { Id = 56, Name = "Solicitar Termos Jurídicos", ModuleId = 26, CreatedUserId = 1, CreatedAt = date, UpdatedUserId = 1, UpdatedAt = date }



        );

        modelBuilder.Entity<TactiumStatusCall>().HasData(
            new { Id = 1, Value = "Sem Sinal" },
            new { Id = 2, Value = "Ocupado" },
            new { Id = 3, Value = "Completado" },
            new { Id = 4, Value = "Telefone Errado" },
            new { Id = 5, Value = "Falha" },
            new { Id = 6, Value = "Bloqueado PROCON" },
            new { Id = 7, Value = "Sem Rota" },
            new { Id = 8, Value = "Bloqueio DDD" },
            new { Id = 9, Value = "Renintência" },
            new { Id = 10, Value = "Bloqueio Lista" }
        );

        modelBuilder.Entity<TactiumTypesCall>().HasData(
            new { Id = 1, Value = "Ativa" },
            new { Id = 2, Value = "Receptiva" },
            new { Id = 3, Value = "Interna" }
        );

        modelBuilder.Entity<TactiumTypesService>().HasData(
            new { Id = 1, Value = "Fax" },
            new { Id = 2, Value = "Não Atende" },
            new { Id = 3, Value = "Fora de Área" },
            new { Id = 4, Value = "Desconhecido" },
            new { Id = 5, Value = "Telefone Bloqueado" },
            new { Id = 6, Value = "Atendimento Humano CPC" },
            new { Id = 7, Value = "Classifição de Mailing" },
            new { Id = 8, Value = "Atendimento Eletrônico" },
            new { Id = 9, Value = "Atendimento Eletrônico Analisador" },
            new { Id = 10, Value = "Atendimento Humano Recado" }
        );

        modelBuilder.Entity<TactiumSituationAgent>().HasData(
            new { Id = 1, Value = "Pausado", EventId = 3 },
            new { Id = 2, Value = "Pronto", EventId = 4 },
            new { Id = 3, Value = "Pós-Atendimento", EventId = 4 },
            new { Id = 4, Value = "indisponível", EventId = 6 },
            new { Id = 5, Value = "Disponível", EventId = 7 }
        );

        modelBuilder.Entity<TactiumStatusAgent>().HasData(
            new { Id = 1, Value = "Logado" },
            new { Id = 2, Value = "Deslogado" }
        );

        modelBuilder.Entity<CalculationTemplate>().HasData(
            new { Id = 1, Name = "Quitação parcelada" },
            new { Id = 2, Name = "Quitação com prévia" },
            new { Id = 3, Name = "Regularização à vista" },
            new { Id = 4, Name = "Regularização parcelada" },
            new { Id = 5, Name = "Desconto no principal" },
            new { Id = 6, Name = "Simulador Entrada Diluição" },
            new { Id = 7, Name = "Simulador Entrada Aditamento" },
            new { Id = 8, Name = "Calculadora Banco Rodobens" },
            new { Id = 9, Name = "Simulador Entrada Aditamento - Consórcio" }
        );

        modelBuilder.Entity<Telephony>().HasData(
            new { Id = 1, Name = "Tactium" },
            new { Id = 2, Name = "Olus" }
        );

        modelBuilder.Entity<DatacobAccess>().HasData(
            new { Id = 1, DatacobName = "Rodobens", DatacobNumber = GvcRodobens.Rodobens, CreatedUserId = 1, UpdatedUserId = 1, CreatedAt = date, UpdatedAt = date },
            new { Id = 2, DatacobName = "GVC", DatacobNumber = GvcRodobens.GVC, CreatedUserId = 1, UpdatedUserId = 1, CreatedAt = date, UpdatedAt = date }
        );

        modelBuilder.Entity<DepartmentModel>().HasData(
            new { Id = new Guid("{6C6A8271-1D30-478E-80F7-E06091551F70}"), Description = "Digital", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{E66FAC33-8077-40BD-A028-DD0193A53A73}"), Description = "RH", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{44D50A63-FD84-4A2B-B6C1-FDC30925C60F}"), Description = "Operação - Rodobens Consórcio", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{BE5C4719-3276-4608-AA99-F6E06D16130F}"), Description = "Operação - Safra", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{394C7BE0-5DE0-4851-B35D-D5DCC457750C}"), Description = "Operação - Banco", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{7FC4C9E1-2200-49B7-BCBC-F45F53633E40}"), Description = "Operação - RNI / GVI", CreatedAt = date, UpdatedAt = date }
           );

        modelBuilder.Entity<NewconAPI>().HasData(
            new { Id = new Guid("{0DAC059B-2B28-4DBD-AE52-04C73CEDF59B}"), Name = "SaldoAcumulado", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{32DD2FA1-EE96-4BB8-890D-0A41B19AB9D6}"), Name = "SaldoAcumulado/ValoresPagos", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{FB1D5057-603B-4E42-844D-0E89E806CE05}"), Name = "EstruturaDeVendas", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{A17AED78-4A23-4F6F-BED8-14D2EDB9BA73}"), Name = "Socios", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{7D419266-D7FD-4104-B137-3283DF33E63D}"), Name = "EvolucaoPrecoBem", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{1B10C6CB-5983-49E6-B74C-4344D4E683E4}"), Name = "Agenda", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{94E753D3-C67D-4013-A384-45EE1E643AF2}"), Name = "Agenda/Protocol", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{7E6DEB78-E9B8-4D69-B079-5D859B23F5E3}"), Name = "Atrasos", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{ED61059C-501F-4ED9-8732-6107C089D699}"), Name = "EstruturaComercial", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{5A8078BB-B209-4B9E-8FF2-9DECAFCF71E7}"), Name = "EstruturaComercial/Detalhes", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{6566F43C-EF64-44EB-8126-9E7A035D83F3}"), Name = "SaldoAcumulado/SaldoDevedor", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{AB32D94A-7054-460E-B61A-A006B8E02869}"), Name = "DebitoConta", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{BE1B9CEA-AB0A-45F2-AC37-B2E6811354DA}"), Name = "ValoresDevolver/Detalhes", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{66BB51D9-81C4-4B98-83AC-B57B8BEB7572}"), Name = "PlanoCobranca", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{BF94AB35-70B8-4A1A-ACF0-BD56CCCE62AD}"), Name = "Bens/Garantia", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{4A2F8677-7765-4E40-A49C-C4BD1A4F46B3}"), Name = "Bens/Contrato", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{5A595C4D-9AB4-4443-B4CF-C7CD2203781D}"), Name = "DadosPrincipaisAtendimento", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{AD9E2739-2382-434A-B28C-CE709D0EECAB}"), Name = "EquipeVenda", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{4C17158E-DBE0-49DD-86C5-F27F21B7F117}"), Name = "PontoVenda", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{fd42b599-e4f6-445a-8704-5b91f7a7077f}"), Name = "ValoresDevolver", CreatedAt = date, UpdatedAt = date }
           );

        modelBuilder.Entity<Service>().HasData(
            new { Id = new Guid("{76bcc508-d892-4670-9204-f1c569782664}"), Name = "CorreiosCEP", Api = "/consultaCEP/consultav2", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },
            //BTG
            new { Id = new Guid("{f6e34747-f5f0-411f-8c12-3985c4516760}"), Name = "FecharTicket", Api = "/BTGTicket/FecharTicket", ApiType = ApiType.BTG, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{af695dd6-7a9e-4f63-af2d-4df0e366af7c}"), Name = "TentativaTicketBatch", Api = "/BTGTicket/TentativaTicketBatch", ApiType = ApiType.BTG, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{33f11b24-35f0-4fc1-9ad6-0044afb3a507}"), Name = "TentativaTicket", Api = "/BTGTicket/TentativaTicket", ApiType = ApiType.BTG, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{efe61da9-2901-4fdc-b7ef-877ff0e2ab5d}"), Name = "AdicionarContrato", Api = "/BTGTicket/AdicionarContrato", ApiType = ApiType.BTG, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{b5d90232-62c3-47e3-bc6d-d87bba6875b2}"), Name = "AbrirTicket", Api = "/BTGTicket/AbrirTicket", ApiType = ApiType.BTG, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{09bc74e2-4820-41c2-b977-27f73a92ae52}"), Name = "BuscarInfo", Api = "/BTGInfo/getInfo", ApiType = ApiType.BTG, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{264f611e-e4f4-4ff9-a5c4-b74c4dfd4207}"), Name = "BuscarToken", Api = "/BTGToken/getToken", ApiType = ApiType.BTG, CreatedAt = date, UpdatedAt = date },
            // Projuris
            new { Id = new Guid("{d10a5747-a635-42a1-bdee-7ff8f2cd5c4e}"), Name = "ProjurisLogin", Api = "/token/login", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{d8fff9fb-baf1-4893-894a-530c71aaccdf}"), Name = "ProjurisCusta", Api = "/consultaProjuris/listaLancamentos", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{0776adb2-3281-4052-ba60-a1b5206d4c69}"), Name = "ProjurisProcesso", Api = "/consultaProjuris/listaProcessos", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{7c0a7332-a907-4862-9fc4-754bdfeb915c}"), Name = "ProjurisEvento", Api = "/consultaProjuris/listaEventos", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{95c266bf-753f-4a5b-a2d7-0bdb3254eb54}"), Name = "ProjurisDesdobramento", Api = "/consultaProjuris/listaDesdobramento", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{e6f1a2b1-1786-4533-92e8-18d4f0eb279d}"), Name = "ProjurisDocumento", Api = "/consultaProjuris/listaDocumentos", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{1296FDE4-0DCC-4843-9AF2-E49B19FCA110}"), Name = "ProjurisEntidade", Api = "/consultaProjuris/entidade", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },

            new { Id = new Guid("{0F2710FE-**************-324F51706FA7}"), Name = "ProjurisProcessoV2", Api = "/consultaProjuris/listaProcessosV2", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{E2F2B94C-4B59-4D70-ACB3-483AA9A1B024}"), Name = "ProjurisDesdobramentoV2", Api = "/consultaProjuris/listaDesdobramentoV2", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{D7B44F3F-84DA-4388-A8CB-81CF7F577980}"), Name = "ProjurisEventoV2", Api = "/consultaProjuris/listaEventosV2", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },
            // BBC
            new { Id = new Guid("{df2e52da-248e-4082-81c2-61f799258d01}"), Name = "BbcSimularAuth", Api = "/BBCSimularAcordo/getToken", ApiType = ApiType.BBC, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{ba789590-d392-4216-b2bc-e927e2eeef98}"), Name = "BbcSimularElegibilidade", Api = "/BBCSimularAcordo/Elegibilidade", ApiType = ApiType.BBC, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{f26d2629-8779-4069-b2ff-b33b9db81649}"), Name = "BbcSimularAcordo", Api = "/BBCSimularAcordo/Simular", ApiType = ApiType.BBC, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{7d0bb203-f723-485b-bf55-786ddc455a9b}"), Name = "BbcCadastroAuth", Api = "/BBCCadastrarAcordo/getToken", ApiType = ApiType.BBC, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{d7142902-95dd-4991-b36e-e32fe194ad45}"), Name = "BbcCadastroAcordo", Api = "/BBCCadastrarAcordo/Cadastrar", ApiType = ApiType.BBC, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{9b9ec768-7fae-477e-b1b6-15ef4a4c4a8d}"), Name = "BbcCadastroConsulta", Api = "/BBCCadastrarAcordo/Consultar", ApiType = ApiType.BBC, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{76f769b6-c35a-410a-9c0c-22928d345563}"), Name = "BbcBoletoAuth", Api = "/BBCBoletoAcordo/getToken", ApiType = ApiType.BBC, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{2b4a60aa-7465-47a1-9ccf-114155ab191c}"), Name = "BbcBoletoObter", Api = "/BBCBoletoAcordo/Obter", ApiType = ApiType.BBC, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{f8fb8e03-3652-4fc1-8873-374643c7afd3}"), Name = "BbcSaldoAuth", Api = "/BBCSaldoOperador/getToken", ApiType = ApiType.BBC, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{029f90f0-3c43-4bf3-a4a4-99970071527a}"), Name = "BbcSaldoConsultar", Api = "/BBCSaldoOperador/Consulta", ApiType = ApiType.BBC, CreatedAt = date, UpdatedAt = date },

            new { Id = new Guid("{fd42b599-e4f6-445a-8704-5b91f7a7077f}"), Name = "AberturaChamado", Api = "/Ocomon/insereOcorrencia", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },
            // Stellantis
            new { Id = new Guid("{b4d9ff2d-c34e-4276-9b99-d8954b68e6e9}"), Name = "StellantisAuth", Api = "/token", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{4292a500-8449-45dd-b6a4-f12102f5f15a}"), Name = "StellantisDadosDevedor", Api = "/VcomCobapi/v1.0.a/DadosDevedor", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{b7f1da29-c29f-4aa6-88f9-99f98571cda9}"), Name = "StellantisContratosAbertos", Api = "/VcomCobapi/v1.0.a/ContratosAbertos", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{96d8a22d-033b-4df2-9ed3-b44f17f33198}"), Name = "StellantisParcelasContrato", Api = "/VcomCobapi/v1.0.a/ParcelasContrato", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{59dd4933-be12-4776-a5fe-d76b3869dff5}"), Name = "StellantisDadosContrato", Api = "/VcomCobapi/v1.0.a/DadosContrato", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{3063DC4B-649F-4C19-AF30-FB7706398E1E}"), Name = "StellantisHistoricosInsercao", Api = "/VcomCobapi/v1.0.a/HistoricosInsercao", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{98714E66-8B06-4B04-9EE3-61E45FA75F0B}"), Name = "StellantisInserirTelefone", Api = "/VcomCobapi/v1.0.a/InserirTelefone", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{39CB7A3A-FB3C-4CC0-A698-B3EB5D3D9E35}"), Name = "StellantisInserirEndereco", Api = "/VcomCobapi/v1.0.a/InserirEndereco", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{85821748-9B9A-4272-AC97-B00E1E57766F}"), Name = "StellantisIncluirEmail", Api = "/VcomCobapi/v1.0.a/IncluirEmail", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{85821748-9B9A-4272-AC97-B3EB5D3D9E35}"), Name = "StellantisCalcular", Api = "/VCServicos/v1.0.a/Calcular", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{85821748-9B9A-4272-AC97-61E45FA75F0B}"), Name = "StellantisBoletoCancelar", Api = "/VCServicos/v1.0.a/CancelarBoleto", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{96d8a22d-033b-4df2-9ed3-61E45FA75F0B}"), Name = "StellantisBoletoImprimir", Api = "/VCServicos/v1.0.a/ImprimirBoleto", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{96d8a22d-033b-4df2-9ed3-d76b3869dff5}"), Name = "StellantisBoletoConsultar", Api = "/VCServicos/v1.0.a/ConsultarBoleto", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{96d8a22d-033b-4df2-9ed3-114155ab191c}"), Name = "StellantisBoletoImprimirSemCalculo", Api = "/VCServicos/v1.0.a/EmitirBoletoSemRevalidarOCalculo", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{96d8a22d-033b-4df2-9ed3-15ef4a4c4a8d}"), Name = "StellantisBoletoMeiosPgto", Api = "/VCServicos/v1.0.a/SelecionarMeiosPgtoBoletosParaImpressaoTitulo", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{96d8a22d-033b-4df2-9ed3-754bdfeb915c}"), Name = "StellantisBoletoRecuperarOpcoes", Api = "/VCServicos/v1.0.a/RecuperarOpcoesDeParcelamento", ApiType = ApiType.Stellantis, CreatedAt = date, UpdatedAt = date },

            new { Id = new Guid("{9735eb90-e16c-40cb-ba75-a12ba20275a9}"), Name = "GerarPixNegociacao", Api = "/negociacao/v1.0.a/gerarPixNegociacao", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{b64398a0-3022-4df8-bb44-8c70039043dc}"), Name = "InsercaoParcelaDatacob", Api = "/DadosCadastrais/v1.0.a/parcela", ApiType = ApiType.Barramento, CreatedAt = date, UpdatedAt = date },

            new { Id = new Guid("{8bd4d37a-fc7a-447b-bac6-4efd278b294c}"), Name = "ConsultaParcelasCob", Api = "", ApiType = ApiType.SoapNewcon, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{069bcd76-b3f5-4160-ae71-c945bc419701}"), Name = "PosicaoConsorciadoServiceConsultarParcelaAtraso", Api = "", ApiType = ApiType.SoapNewcon, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{79df21b2-7849-4e7a-aa02-74daa5a3664f}"), Name = "ValoresPagosServiceConsultarValoresPagos", Api = "", ApiType = ApiType.SoapNewcon, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{0a991284-3572-4055-aefb-4c1dee0222f4}"), Name = "AgendaServiceConsultarOcorrenciasCota", Api = "", ApiType = ApiType.SoapNewcon, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{7475e2af-a314-4176-8c53-a0322e1069dd}"), Name = "EstruturaVendaService", Api = "", ApiType = ApiType.SoapNewcon, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{9937c112-9646-4937-83a3-02d71b7c39d9}"), Name = "PrecoBemServiceConsultarEvolucaoPrecoBem", Api = "", ApiType = ApiType.SoapNewcon, CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{d44f126a-7287-4fad-a15b-2edb734df5bd}"), Name = "CotaServiceConsultarSaldoAcumuladoCota", Api = "", ApiType = ApiType.SoapNewcon, CreatedAt = date, UpdatedAt = date }
           );

        modelBuilder.Entity<MktzapCompany>().HasData(
            new { Id = new Guid("{18cbe615-2dde-48d4-ac2e-3b3d6950183f}"), CompanyId = 196, ClientKey = "e3e69a81bd8c462eae11d192bb803240", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{f8100d6f-ebe3-4905-a60e-d7154cf7b95a}"), CompanyId = 2809, ClientKey = "f36751d826034a13a750b58095ea7ae4", CreatedAt = date, UpdatedAt = date },
            new { Id = new Guid("{11321348-44fa-45c7-94d1-bdf28883797d}"), CompanyId = 1611, ClientKey = "ef22711472dc4b299663efd405bb951e", CreatedAt = date, UpdatedAt = date }
           );

        modelBuilder.Entity<BbcProducts>().HasData(
            new { Id = new Guid("{317cef71-03cf-4b76-a664-6fb98f39f75f}"), Product = 8000, Label = "Parcelamento", Taxa = 1, CreatedAt = DateTime.Parse("2024-07-11"), UpdatedAt = DateTime.Parse("2024-07-11") }
           );

        modelBuilder.Entity<BbcConfigs>().HasData(
            new { Id = new Guid("{ba23c6a7-8011-4944-a89f-4cfbf8776941}"), IdProtocolo = "", CodCobradora = "004", CodBackOffice = "01", CreatedAt = DateTime.Parse("2024-07-11"), UpdatedAt = DateTime.Parse("2024-07-11") }
            );

        modelBuilder.Entity<NewconCotaValorParcela>(entity =>
        {
            entity.Property(e => e.PercentualContribuicaoMensal).HasPrecision(10, 4);
            entity.Property(e => e.PercentualFundoComum).HasPrecision(10, 4);
            entity.Property(e => e.PercentualFundoReserva).HasPrecision(10, 4);
            entity.Property(e => e.PercentualOutros).HasPrecision(10, 4);
            entity.Property(e => e.PercentualSeguro).HasPrecision(10, 4);
            entity.Property(e => e.PercentualTaxaAdministracao).HasPrecision(10, 4);
            entity.Property(e => e.Seguro).HasPrecision(18, 2);
            entity.Property(e => e.TaxaAdministracao).HasPrecision(18, 2);
            entity.Property(e => e.Adesao).HasPrecision(18, 2);
            entity.Property(e => e.ContribuicaoMensal).HasPrecision(18, 2);
            entity.Property(e => e.FundoComum).HasPrecision(18, 2);
            entity.Property(e => e.FundoReserva).HasPrecision(18, 2);
            entity.Property(e => e.Outros).HasPrecision(18, 2);
            entity.Property(e => e.PercentualAdesao).HasPrecision(8, 4);
        });

        modelBuilder.Entity<NewconCotaValoresPago>(entity =>
        {
            entity.Property(e => e.PercentualDiferenca).HasPrecision(10, 4);
            entity.Property(e => e.PercentualNormal).HasPrecision(10, 4);
            entity.Property(e => e.Valor).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconBemObjeto>(entity =>
        {
            entity.Property(e => e.CreditoCorrigido).HasPrecision(18, 2);
            entity.Property(e => e.CreditoOriginal).HasPrecision(18, 2);
            entity.Property(e => e.FGTS).HasPrecision(18, 2);
            entity.Property(e => e.PagamentosRealizados).HasPrecision(18, 2);
            entity.Property(e => e.Rendimento).HasPrecision(18, 2);
            entity.Property(e => e.TaxasPendentes).HasPrecision(18, 2);
            entity.Property(e => e.ValorAPagar).HasPrecision(18, 2);
            entity.Property(e => e.ValorAntecipacao).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconBemObjetoPagamento>(entity =>
        {
            entity.Property(e => e.PercentualNormal).HasPrecision(10, 4);
            entity.Property(e => e.PercentualDiferenca).HasPrecision(10, 4);
            entity.Property(e => e.Valor).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconCotaSaldoDevedor>(entity =>
        {
            entity.Property(e => e.PagoFundoComum).HasPrecision(18, 2);
            entity.Property(e => e.PagoFundoReserva).HasPrecision(18, 2);
            entity.Property(e => e.PagoTaxaAdministracao).HasPrecision(18, 2);
            entity.Property(e => e.PagoAdesao).HasPrecision(18, 2);
            entity.Property(e => e.PagoSeguros).HasPrecision(18, 2);
            entity.Property(e => e.PagoMultas).HasPrecision(18, 2);
            entity.Property(e => e.PagoJuros).HasPrecision(18, 2);
            entity.Property(e => e.PagoTotal).HasPrecision(18, 2);
            entity.Property(e => e.DevedorFundoComum).HasPrecision(18, 2);
            entity.Property(e => e.DevedorFundoReserva).HasPrecision(18, 2);
            entity.Property(e => e.DevedorTaxaAdministracao).HasPrecision(18, 2);
            entity.Property(e => e.DevedorAdesao).HasPrecision(18, 2);
            entity.Property(e => e.DevedorSeguros).HasPrecision(18, 2);
            entity.Property(e => e.DevedorMultas).HasPrecision(18, 2);
            entity.Property(e => e.DevedorJuros).HasPrecision(18, 2);
            entity.Property(e => e.DevedorTotal).HasPrecision(18, 2);
            entity.Property(e => e.PagoOutrosValores).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconCotaDifParcela>(entity =>
        {
            entity.Property(e => e.FundoComum).HasPrecision(18, 2);
            entity.Property(e => e.FundoReserva).HasPrecision(18, 2);
            entity.Property(e => e.TaxaAdministracao).HasPrecision(18, 2);
            entity.Property(e => e.Total).HasPrecision(18, 2);
            entity.Property(e => e.NegociadaFundoComum).HasPrecision(18, 2);
            entity.Property(e => e.NegociadaFundoReserva).HasPrecision(18, 2);
            entity.Property(e => e.NegociadaTaxaAdministracao).HasPrecision(18, 2);
            entity.Property(e => e.NegociadaTotal).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconBemObjeto>(entity =>
        {
            entity.Property(e => e.CreditoCorrigido).HasPrecision(18, 2);
            entity.Property(e => e.CreditoOriginal).HasPrecision(18, 2);
            entity.Property(e => e.FGTS).HasPrecision(18, 2);
            entity.Property(e => e.PagamentosRealizados).HasPrecision(18, 2);
            entity.Property(e => e.Rendimento).HasPrecision(18, 2);
            entity.Property(e => e.TaxasPendentes).HasPrecision(18, 2);
            entity.Property(e => e.ValorAPagar).HasPrecision(18, 2);
            entity.Property(e => e.ValorAntecipacao).HasPrecision(18, 2);
            entity.Property(e => e.ValorBem).HasPrecision(18, 2);
            entity.Property(e => e.ValorPago).HasPrecision(18, 2);
            entity.Property(e => e.ValorPendenteEntrega).HasPrecision(18, 2);
            entity.Property(e => e.ValorQuitacao).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconBemObjetoFgts>(entity =>
        {
            entity.Property(e => e.ValorRepasse).HasPrecision(18, 2);
            entity.Property(e => e.ValorSolicitacao).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconBemObjetoFgtsMensal>(entity =>
        {
            entity.Property(e => e.Valor).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconBemObjetoRendimento>(entity =>
        {
            entity.Property(e => e.TotalRendimento).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconBemObjetoRendimentoMensal>(entity =>
        {
            entity.Property(e => e.ValorIR).HasPrecision(18, 2);
            entity.Property(e => e.ValorRendimento).HasPrecision(18, 2);
            entity.Property(e => e.ValorRendimentoLiquido).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconCota>(entity =>
        {
            entity.Property(e => e.ArasoPercentual).HasPrecision(8, 4);    // Note: Typo in property name (should be AtrasoPercentual?)
            entity.Property(e => e.Atraso).HasPrecision(18, 2);
            entity.Property(e => e.DiferencaParcela).HasPrecision(18, 2);
            entity.Property(e => e.DiferencaParcelaPercentual).HasPrecision(8, 4);
            entity.Property(e => e.FundoReserva).HasPrecision(18, 2);
            entity.Property(e => e.PercentualCont).HasPrecision(8, 4);
            entity.Property(e => e.SaldoDevedor).HasPrecision(18, 2);
            entity.Property(e => e.SaldoDevedorPercentual).HasPrecision(8, 4);
            entity.Property(e => e.TaxaAdesao).HasPrecision(18, 2);
            entity.Property(e => e.TaxaAdminstracao).HasPrecision(18, 2);  // Note: Typo in property name (should be TaxaAdministracao?)
            entity.Property(e => e.ValorBem).HasPrecision(18, 2);
            entity.Property(e => e.ValorParcela).HasPrecision(18, 2);
            entity.Property(e => e.ValorParcelaPercentual).HasPrecision(8, 4);
            entity.Property(e => e.ValoresPago).HasPrecision(18, 2);
            entity.Property(e => e.ValoresPagoPercentual).HasPrecision(8, 4);
        });

        modelBuilder.Entity<NewconCotaAtraso>(entity =>
        {
            entity.Property(e => e.PercentualAdesao).HasPrecision(8, 4);
            entity.Property(e => e.PercentualIdeal).HasPrecision(8, 4);
            entity.Property(e => e.Total).HasPrecision(18, 2);
            entity.Property(e => e.ValorJuros).HasPrecision(18, 2);
            entity.Property(e => e.ValorMulta).HasPrecision(18, 2);
            entity.Property(e => e.ValorParcela).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconCotaCobrancaParcela>(entity =>
        {
            entity.Property(e => e.Juros).HasPrecision(18, 2);
            entity.Property(e => e.Multa).HasPrecision(18, 2);
            entity.Property(e => e.Valor).HasPrecision(18, 2);
            entity.Property(e => e.ValorQuitacao).HasPrecision(18, 2);
            entity.Property(e => e.ValorReceber).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconCotaDifParcela>(entity =>
        {
            entity.Property(e => e.FundoComum).HasPrecision(18, 2);
            entity.Property(e => e.FundoReserva).HasPrecision(18, 2);
            entity.Property(e => e.NegociadaFundoComum).HasPrecision(18, 2);
            entity.Property(e => e.NegociadaFundoReserva).HasPrecision(18, 2);
            entity.Property(e => e.NegociadaPercentualFundoComum).HasPrecision(8, 4);
            entity.Property(e => e.NegociadaPercentualFundoReserva).HasPrecision(8, 4);
            entity.Property(e => e.NegociadaPercentualTaxaAdministracao).HasPrecision(8, 4);
            entity.Property(e => e.NegociadaPercentualTotal).HasPrecision(8, 4);
            entity.Property(e => e.NegociadaTaxaAdministracao).HasPrecision(18, 2);
            entity.Property(e => e.NegociadaTotal).HasPrecision(18, 2);
            entity.Property(e => e.PercentualFundoComum).HasPrecision(8, 4);
            entity.Property(e => e.PercentualFundoReserva).HasPrecision(8, 4);
            entity.Property(e => e.PercentualTaxaAdministracao).HasPrecision(8, 4);
            entity.Property(e => e.PercentualTotal).HasPrecision(8, 4);
            entity.Property(e => e.TaxaAdministracao).HasPrecision(18, 2);
            entity.Property(e => e.Total).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconCotaNegociacao>(entity =>
        {
            entity.Property(e => e.RateioFundoComum).HasPrecision(18, 2);
            entity.Property(e => e.RateioFundoReserva).HasPrecision(18, 2);
            entity.Property(e => e.RateioOutros).HasPrecision(18, 2);
            entity.Property(e => e.RateioTaxaAdesao).HasPrecision(18, 2);
            entity.Property(e => e.RateioTaxaAdministracao).HasPrecision(18, 2);
        });

        modelBuilder.Entity<NewconCotaNegociacaoParcela>(entity =>
        {
            entity.Property(e => e.PercAdesao).HasPrecision(8, 4);
            entity.Property(e => e.PercFundoComum).HasPrecision(8, 4);
            entity.Property(e => e.PercFundoReserva).HasPrecision(8, 4);
            entity.Property(e => e.PercOutros).HasPrecision(8, 4);
            entity.Property(e => e.PercTaxaAdministracao).HasPrecision(8, 4);
        });

        modelBuilder.Entity<NewconCotaSaldoDevedor>(entity =>
        {
            entity.Property(e => e.DevedorAdesao).HasPrecision(18, 2);
            entity.Property(e => e.DevedorAdesaoPercentual).HasPrecision(18, 4);
            entity.Property(e => e.DevedorDiferencaParcela).HasPrecision(18, 2);
            entity.Property(e => e.DevedorDiferencaParcelaPercentual).HasPrecision(18, 4);
            entity.Property(e => e.DevedorFundoComum).HasPrecision(18, 2);
            entity.Property(e => e.DevedorFundoComumPercentual).HasPrecision(18, 4);
            entity.Property(e => e.DevedorFundoReserva).HasPrecision(18, 2);
            entity.Property(e => e.DevedorFundoReservaPercentual).HasPrecision(18, 4);
            entity.Property(e => e.DevedorJuros).HasPrecision(18, 2);
            entity.Property(e => e.DevedorJurosPercentual).HasPrecision(18, 4);
            entity.Property(e => e.DevedorMultas).HasPrecision(18, 2);
            entity.Property(e => e.DevedorMultasPercentual).HasPrecision(18, 4);
            entity.Property(e => e.DevedorOutrosValores).HasPrecision(18, 2);
            entity.Property(e => e.DevedorOutrosValoresPercentual).HasPrecision(18, 4);
            entity.Property(e => e.DevedorReajusteSaldoCaixa).HasPrecision(18, 2);
            entity.Property(e => e.DevedorReajusteSaldoCaixaPercentual).HasPrecision(18, 4);
            entity.Property(e => e.DevedorSeguros).HasPrecision(18, 2);
            entity.Property(e => e.DevedorSegurosPercentual).HasPrecision(18, 4);
            entity.Property(e => e.DevedorTaxaAdministracao).HasPrecision(18, 2);
            entity.Property(e => e.DevedorTaxaAdministracaoPercentual).HasPrecision(18, 4);
            entity.Property(e => e.DevedorTxAdmReajSaldoCaixa).HasPrecision(18, 2);
            entity.Property(e => e.DevedorTxAdmReajSaldoCaixaPercentual).HasPrecision(18, 4);
            entity.Property(e => e.DevedorTotal).HasPrecision(18, 2);
            entity.Property(e => e.ParcelasDevedor).HasPrecision(18, 2);
            entity.Property(e => e.PercentualParcelasDevedor).HasPrecision(18, 4);
            entity.Property(e => e.PagoAdesao).HasPrecision(18, 2);
            entity.Property(e => e.PagoAdesaoPercentual).HasPrecision(18, 4);
            entity.Property(e => e.PagoFundoComum).HasPrecision(18, 2);
            entity.Property(e => e.PagoFundoComumPercentual).HasPrecision(18, 4);
            entity.Property(e => e.PagoFundoReserva).HasPrecision(18, 2);
            entity.Property(e => e.PagoFundoReservaPercentual).HasPrecision(18, 4);
            entity.Property(e => e.PagoImpostos).HasPrecision(18, 2);
            entity.Property(e => e.PagoJuros).HasPrecision(18, 2);
            entity.Property(e => e.PagoMultas).HasPrecision(18, 2);
            entity.Property(e => e.PagoSeguros).HasPrecision(18, 2);
            entity.Property(e => e.PagoReajusteSaldoCaixa).HasPrecision(18, 2);
            entity.Property(e => e.PagoReajusteSaldoCaixaPercentual).HasPrecision(18, 4);
            entity.Property(e => e.PagoTaxaAdministracao).HasPrecision(18, 2);
            entity.Property(e => e.PagoTaxaAdministracaoPercentual).HasPrecision(18, 4);
            entity.Property(e => e.PagoTotal).HasPrecision(18, 2);
            entity.Property(e => e.PagoTxAdmReajSaldoCaixa).HasPrecision(18, 2);
            entity.Property(e => e.PagoTxAdmReajSaldoCaixaPercentual).HasPrecision(18, 4);
            entity.Property(e => e.PercentualPago).HasPrecision(8, 4);
            entity.Property(e => e.PercentualAtraso).HasPrecision(8, 4);
            entity.Property(e => e.PercentualCobrar).HasPrecision(8, 4);
            entity.Property(e => e.PercentualTotal).HasPrecision(8, 4);
            entity.Property(e => e.PercentualMensal).HasPrecision(8, 4);
            entity.Property(e => e.PercentualIdealPago).HasPrecision(8, 4);
            entity.Property(e => e.PercentualDiferenca).HasPrecision(8, 4);
            entity.Property(e => e.PercentualIdealDevido).HasPrecision(8, 4);
            entity.Property(e => e.PercentualPagoAntecipando).HasPrecision(8, 4);
            entity.Property(e => e.PercentualAtrasoAntecipando).HasPrecision(8, 4);
            entity.Property(e => e.PercentualCobrarAntecipando).HasPrecision(8, 4);
            entity.Property(e => e.PercentualTotalAntecipando).HasPrecision(8, 4);
            entity.Property(e => e.PercentualTotalPago).HasPrecision(8, 4);
            entity.Property(e => e.PercentualTotalAtraso).HasPrecision(8, 4);
            entity.Property(e => e.PercentualTotalCobrar).HasPrecision(8, 4);
            entity.Property(e => e.PercentualTotalTotal).HasPrecision(8, 4);
        });

        modelBuilder.Entity<NewconCotaSaldoDevedorOutrosValor>(entity =>
        {
            entity.Property(e => e.Valor).HasPrecision(18, 2);
        });

        modelBuilder.Entity<DatacobInvoices>(entity =>
        {
            entity.HasOne(e => e.User)
                  .WithMany()
                  .HasForeignKey(e => e.UserId)
                  .IsRequired(false); // Make optional
        });

        modelBuilder.Entity<DatacobJokerTicket>(entity =>
        {
            entity.HasOne(e => e.User)
                  .WithMany()
                  .HasForeignKey(e => e.UserId)
                  .IsRequired(false); // Make optional
        });

        modelBuilder.Entity<DatacobOccurrence>(entity =>
        {
            entity.HasOne(e => e.User)
                  .WithMany()
                  .HasForeignKey(e => e.UserId)
                  .IsRequired(false); // Make optional
        });

        modelBuilder.Entity<DatacobPhoneCall>(entity =>
        {
            entity.HasOne(e => e.User)
                  .WithMany()
                  .HasForeignKey(e => e.UserId)
                  .IsRequired(false); // Make optional
        });

        modelBuilder.Entity<LogErrorApp>(entity =>
        {
            entity.HasOne(e => e.User)
                  .WithMany()
                  .HasForeignKey(e => e.UserId)
                  .IsRequired(false); // Make optional
        });

        // For CorJuridicoParam relationship
        modelBuilder.Entity<SafraCampaignRestriction>(entity =>
        {
            entity.HasOne(e => e.CorJuridicoParam)
                  .WithMany()
                  .HasForeignKey(e => e.CorJuridicoParamId)
                  .IsRequired(false); // Make optional
        });
    }

    public DbSet<User> Users => Set<User>();
    public DbSet<UserSession> UserSessions => Set<UserSession>();
    public DbSet<Role> Roles => Set<Role>();
    public DbSet<Module> Modules => Set<Module>();
    public DbSet<Mailing> Mailings => Set<Mailing>();
    public DbSet<MailingContract> MailingContracts => Set<MailingContract>();
    public DbSet<MailingUser> MailingUsers => Set<MailingUser>();
    public DbSet<SubModule> SubModules => Set<SubModule>();
    public DbSet<ModuleRole> ModuleRoles => Set<ModuleRole>();
    public DbSet<SubModuleRole> SubModuleRoles => Set<SubModuleRole>();
    // public DbSet<UserRoles> UserRoles => Set<UserRoles>();
    public DbSet<Config> Configs => Set<Config>();
    public DbSet<ConfigurationCrm> ConfigurationCrms => Set<ConfigurationCrm>();
    public DbSet<Group> Groups => Set<Group>();
    public DbSet<UserGroup> UserGroups => Set<UserGroup>();
    public DbSet<ClientGroup> ClientGroups => Set<ClientGroup>();
    // public DbSet<Client> Clients => Set<Client>();
    public DbSet<LogIntegracao> LogIntegracoes => Set<LogIntegracao>();
    public DbSet<CustasDatacob> CustasDatacob => Set<CustasDatacob>();
    public DbSet<MktzapMessage> MktzapMessages => Set<MktzapMessage>();
    public DbSet<TactiumStatusCall> TactiumStatusCall => Set<TactiumStatusCall>();
    public DbSet<TactiumTypesCall> TactiumTypesCall => Set<TactiumTypesCall>();
    public DbSet<TactiumTypesService> TactiumTypesService => Set<TactiumTypesService>();
    public DbSet<DelayRangeNewcon> DelayRangeNewcon => Set<DelayRangeNewcon>();
    public DbSet<CalculationTemplate> CalculationTemplate => Set<CalculationTemplate>();
    public DbSet<DownloadDocProjuris> DownloadDocProjuris => Set<DownloadDocProjuris>();
    public DbSet<WebsocketConnections> WebsocketConnections => Set<WebsocketConnections>();

    public DbSet<FinancedModel> FinancedModels => Set<FinancedModel>();
    public DbSet<ContractModel> ContractModels => Set<ContractModel>();
    public DbSet<ContractInstalmentModel> ContractInstalmentModels => Set<ContractInstalmentModel>();
    public DbSet<AgreementModel> AgreementModels => Set<AgreementModel>();
    public DbSet<AgreementInstallmentModel> AgreementInstallmentModels => Set<AgreementInstallmentModel>();
    public DbSet<AgreementTicketModel> AgreementTicketModel => Set<AgreementTicketModel>();
    public DbSet<InvoiceModel> InvoiceModels => Set<InvoiceModel>();
    public DbSet<DatacobAccess> DatacobAccess => Set<DatacobAccess>();
    public DbSet<UserDatacobAccess> UserDatacobAccess => Set<UserDatacobAccess>();
    public DbSet<FunnelSafraModel> FunnelSafraModels => Set<FunnelSafraModel>();

    public DbSet<TradingRestrictionModel> TradingRestrictionModel => Set<TradingRestrictionModel>();
    public DbSet<RestrictionAnalysisRequestModel> RestrictionAnalysisRequestModel => Set<RestrictionAnalysisRequestModel>();

    // Telefonia
    public DbSet<Telephony> Telephony => Set<Telephony>();
    public DbSet<AgentFone> AgentFone => Set<AgentFone>();
    public DbSet<AgentCall> AgentCall => Set<AgentCall>();
    public DbSet<AgentFonePause> AgentFonePause => Set<AgentFonePause>();
    public DbSet<PauseMotive> PauseMotive => Set<PauseMotive>();
    public DbSet<CalculationDischargeWithPrior> CalculationDischargeWithPrior => Set<CalculationDischargeWithPrior>();
    public DbSet<CalculationPaymentInInstallments> CalculationPaymentInInstallments => Set<CalculationPaymentInInstallments>();
    public DbSet<CallCampaign> CallCampaign => Set<CallCampaign>();
    public DbSet<AgentCampaign> AgentCampaign => Set<AgentCampaign>();
    public DbSet<CalculationMainDiscount> CalculationMainDiscount => Set<CalculationMainDiscount>();
    public DbSet<CalculationCashSettlement> CalculationCashSettlement => Set<CalculationCashSettlement>();
    public DbSet<CalculationInstallmentRegularization> CalculationInstallmentRegularization => Set<CalculationInstallmentRegularization>();
    public DbSet<GroupCalculationDatacob> GroupCalculationDatacob => Set<GroupCalculationDatacob>();
    public DbSet<TactiumToken> TactiumToken => Set<TactiumToken>();
    public DbSet<PauseTactium> PauseTactium => Set<PauseTactium>();

    /*Support*/
    public DbSet<DepartmentModel> DepartmentModel => Set<DepartmentModel>();
    /*Support*/

    public DbSet<NewconAPI> NewconAPI => Set<NewconAPI>();
    public DbSet<NewconAccess> NewconAccess => Set<NewconAccess>();
    public DbSet<DatacobInvoices> DatacobInvoices => Set<DatacobInvoices>();
    public DbSet<Service> Service => Set<Service>();
    public DbSet<DatacobOccurrence> DatacobOccurrence => Set<DatacobOccurrence>();
    public DbSet<DatacobPhoneCall> DatacobPhoneCall => Set<DatacobPhoneCall>();
    public DbSet<MktzapCompany> MktzapCompany => Set<MktzapCompany>();
    public DbSet<RpaOccurrenceTriggers> RpaOccurrenceTriggers => Set<RpaOccurrenceTriggers>();
    public DbSet<TransferCampaign> TransferCampaign => Set<TransferCampaign>();
    public DbSet<UserCrm> UserCrm => Set<UserCrm>();

    /*Parametros de Calculos*/
    public DbSet<CustomerDataCobParametersCalculationsModel> CalculationParameter => Set<CustomerDataCobParametersCalculationsModel>();


    /*BTG*/
    public DbSet<TicketBTG> TicketBTG => Set<TicketBTG>();
    public DbSet<CodParamBTG> CodParamBTG => Set<CodParamBTG>();
    public DbSet<BtgAttemptBatchContracts> BtgAttemptBatchContracts => Set<BtgAttemptBatchContracts>();
    public DbSet<BtgAttemptBatch> BtgAttemptBatch => Set<BtgAttemptBatch>();
    /*BTG*/

    public DbSet<DatacobSaveNegotiation> DatacobSaveNegotiation => Set<DatacobSaveNegotiation>();
    public DbSet<TUNegociacaoCalculoLivre> TUNegociacaoCalculoLivre => Set<TUNegociacaoCalculoLivre>();
    public DbSet<DatacobJokerTicket> DatacobJokerTicket => Set<DatacobJokerTicket>();
    public DbSet<DatacobInvoicesDataNegotiation> DatacobInvoicesDataNegotiation => Set<DatacobInvoicesDataNegotiation>();

    public DbSet<LogUser> LogUser => Set<LogUser>();
    public DbSet<LogErrorApp> LogErrorApp => Set<LogErrorApp>();
    public DbSet<LogApi> LogApi => Set<LogApi>();
    public DbSet<BbcAuth> BbcAuth => Set<BbcAuth>();
    public DbSet<Notification> Notification => Set<Notification>();
    public DbSet<BbcProducts> BbcProducts => Set<BbcProducts>();
    public DbSet<BbcConfigs> BbcConfigs => Set<BbcConfigs>();
    public DbSet<Agreement> Agreement => Set<Agreement>();
    public DbSet<AgreementSimulation> AgreementSimulation => Set<AgreementSimulation>();
    public DbSet<AgreementInstallment> AgreementInstallment => Set<AgreementInstallment>();
    public DbSet<AgreementSimulationInstallment> AgreementSimulationInstallment => Set<AgreementSimulationInstallment>();
    public DbSet<AgreementSimulationInstallmentInput> AgreementSimulationInstallmentInput => Set<AgreementSimulationInstallmentInput>();
    public DbSet<BbcInstallmentParam> BbcInstallmentParam => Set<BbcInstallmentParam>();
    public DbSet<BbcDiscountParam> BbcDiscountParam => Set<BbcDiscountParam>();
    public DbSet<MissedOccurrence> MissedOccurrence => Set<MissedOccurrence>();
    public DbSet<CorJuridicoParam> CorJuridicoParam => Set<CorJuridicoParam>();

    /* Campanhas Safra */
    public DbSet<SafraCampaignPermissions> SafraCampaignPermissions => Set<SafraCampaignPermissions>();
    public DbSet<SafraCampaign> SafraCampaign => Set<SafraCampaign>();
    public DbSet<SafraCampaignItem> SafraCampaignItem => Set<SafraCampaignItem>();
    public DbSet<UserControlTicket> UserControlTicket => Set<UserControlTicket>();

    /* Topics FAQS */
    public DbSet<Topic> Topic => Set<Topic>();
    public DbSet<TopicDocument> TopicDocument => Set<TopicDocument>();

    /* Simulador Aditamento e Diluição */
    public DbSet<CalculationAdditionEntrySimulator> CalculationAdditionEntrySimulator => Set<CalculationAdditionEntrySimulator>();
    public DbSet<CalculationDilutionEntrySimulator> CalculationDilutionEntrySimulator => Set<CalculationDilutionEntrySimulator>();
    public DbSet<AdditionEntrySimulatorDelayParam> AdditionEntrySimulatorDelayParam => Set<AdditionEntrySimulatorDelayParam>();

    public DbSet<CustomerHistory> CustomerHistory => Set<CustomerHistory>();

    /* OccurrenceRules */
    public DbSet<OccurrenceRules> OccurrenceRules => Set<OccurrenceRules>();
    public DbSet<OccurrenceRulesCustomFields> OccurrenceRulesCustomFields => Set<OccurrenceRulesCustomFields>();
    public DbSet<OccurrenceRulesEmails> OccurrenceRulesEmails => Set<OccurrenceRulesEmails>();

    /* Send From Email */
    public DbSet<SendFromEmail> SendFromEmail => Set<SendFromEmail>();
    public DbSet<SendFromEmailAttempt> SendFromEmailAttempt => Set<SendFromEmailAttempt>();
    public DbSet<SendFromEmailAttachment> SendFromEmailAttachment => Set<SendFromEmailAttachment>();

    public DbSet<IntegrationConfiguration> IntegrationConfigurations => Set<IntegrationConfiguration>();

    /* Newcom */
    public DbSet<NewconAssembleia> NewconAssembleia => Set<NewconAssembleia>();
    public DbSet<NewconBemObjeto> NewconBemObjeto => Set<NewconBemObjeto>();
    public DbSet<NewconBemObjetoFgts> NewconBemObjetoFgts => Set<NewconBemObjetoFgts>();
    public DbSet<NewconBemObjetoFgtsMensal> NewconBemObjetoFgtsMensal => Set<NewconBemObjetoFgtsMensal>();
    public DbSet<NewconBemObjetoPagamento> NewconBemObjetoPagamento => Set<NewconBemObjetoPagamento>();
    public DbSet<NewconBemObjetoRendimento> NewconBemObjetoRendimento => Set<NewconBemObjetoRendimento>();
    public DbSet<NewconBemObjetoRendimentoMensal> NewconBemObjetoRendimentoMensal => Set<NewconBemObjetoRendimentoMensal>();
    public DbSet<NewconCliente> NewconCliente => Set<NewconCliente>();
    public DbSet<NewconClienteBanco> NewconClienteBanco => Set<NewconClienteBanco>();
    public DbSet<NewconContemplacao> NewconContemplacao => Set<NewconContemplacao>();
    public DbSet<NewconCota> NewconCota => Set<NewconCota>();
    public DbSet<NewconCotaAgenda> NewconCotaAgenda => Set<NewconCotaAgenda>();
    public DbSet<NewconCotaAtraso> NewconCotaAtraso => Set<NewconCotaAtraso>();
    public DbSet<NewconCotaCobranca> NewconCotaCobranca => Set<NewconCotaCobranca>();
    public DbSet<NewconCotaCobrancaParcela> NewconCotaCobrancaParcela => Set<NewconCotaCobrancaParcela>();
    public DbSet<NewconCotaDadoCota> NewconCotaDadoCota => Set<NewconCotaDadoCota>();
    public DbSet<NewconCotaDadoCotaBanco> NewconCotaDadoCotaBanco => Set<NewconCotaDadoCotaBanco>();
    public DbSet<NewconCotaDifParcela> NewconCotaDifParcela => Set<NewconCotaDifParcela>();
    public DbSet<NewconCotaEnvolvido> NewconCotaEnvolvido => Set<NewconCotaEnvolvido>();
    public DbSet<NewconCotaEnvolvidoCota> NewconCotaEnvolvidoCota => Set<NewconCotaEnvolvidoCota>();
    public DbSet<NewconCotaEnvolvidoVigencia> NewconCotaEnvolvidoVigencia => Set<NewconCotaEnvolvidoVigencia>();
    public DbSet<NewconCotaNegociacao> NewconCotaNegociacao => Set<NewconCotaNegociacao>();
    public DbSet<NewconCotaNegociacaoParcela> NewconCotaNegociacaoParcela => Set<NewconCotaNegociacaoParcela>();
    public DbSet<NewconCotaReportavel> NewconCotaReportavel => Set<NewconCotaReportavel>();
    public DbSet<NewconCotaRepresentante> NewconCotaRepresentante => Set<NewconCotaRepresentante>();
    public DbSet<NewconCotaSaldoDevedor> NewconCotaSaldoDevedor => Set<NewconCotaSaldoDevedor>();
    public DbSet<NewconCotaSaldoDevedorOutrosValor> NewconCotaSaldoDevedorOutrosValor => Set<NewconCotaSaldoDevedorOutrosValor>();
    public DbSet<NewconCotaTitularidade> NewconCotaTitularidade => Set<NewconCotaTitularidade>();
    public DbSet<NewconCotaTitularidadeVigencia> NewconCotaTitularidadeVigencia => Set<NewconCotaTitularidadeVigencia>();
    public DbSet<NewconCotaValoresPago> NewconCotaValoresPago => Set<NewconCotaValoresPago>();
    public DbSet<NewconCotaValorParcela> NewconCotaValorParcela => Set<NewconCotaValorParcela>();
    public DbSet<NewconDesclassificacao> NewconDesclassificacao => Set<NewconDesclassificacao>();
    public DbSet<NewconDesclassificacaoFase> NewconDesclassificacaoFase => Set<NewconDesclassificacaoFase>();
    public DbSet<NewconDifGrupo> NewconDifGrupo => Set<NewconDifGrupo>();
    public DbSet<NewconEmpresa> NewconEmpresa => Set<NewconEmpresa>();
    public DbSet<NewconFilial> NewconFilial => Set<NewconFilial>();
    public DbSet<NewconPessoa> NewconPessoa => Set<NewconPessoa>();
    public DbSet<NewconPessoaContato> NewconPessoaContato => Set<NewconPessoaContato>();
    public DbSet<NewconPessoaEndereco> NewconPessoaEndereco => Set<NewconPessoaEndereco>();
    public DbSet<NewconPlanoVenda> NewconPlanoVenda => Set<NewconPlanoVenda>();
    public DbSet<NewconPontoEntrega> NewconPontoEntrega => Set<NewconPontoEntrega>();
    public DbSet<NewconPontoVenda> NewconPontoVenda => Set<NewconPontoVenda>();
    public DbSet<NewconRecuperacao> NewconRecuperacao => Set<NewconRecuperacao>();
    public DbSet<NewconSuspensao> NewconSuspensao => Set<NewconSuspensao>();
    public DbSet<NewconEquipeVenda> NewconEquipeVenda => Set<NewconEquipeVenda>();
    public DbSet<NewconComissionado> NewconComissionado => Set<NewconComissionado>();
    public DbSet<NewconUnidade> NewconUnidade => Set<NewconUnidade>();
    public DbSet<NewconRoboSemafaro> NewconRoboSemafaro => Set<NewconRoboSemafaro>();


    public DbSet<AcordoManual> AcordoManual => Set<AcordoManual>();
    public DbSet<AcordoManualParcela> AcordoManualParcela => Set<AcordoManualParcela>();

    public DbSet<PrestadoresServicos> PrestadoresServicos => Set<PrestadoresServicos>();

    public DbSet<TipoCartaETermo> TipoCartaETermo => Set<TipoCartaETermo>();
    public DbSet<PedidoCartasETermos> PedidoCartasETermos => Set<PedidoCartasETermos>();
    public DbSet<PedidoTermoInfos> PedidoTermoInfos => Set<PedidoTermoInfos>();
    public DbSet<PedidoTermoParcelas> PedidoTermoParcelas => Set<PedidoTermoParcelas>();
    public DbSet<ConteudoTermo> ConteudoTermo => Set<ConteudoTermo>();
    public DbSet<TipoTermo> TipoTermo => Set<TipoTermo>();
    public DbSet<DatacobPixHistory> DatacobPixHistory => Set<DatacobPixHistory>();

    // Stellantis
    public DbSet<StellantisAuth> StellantisAuth => Set<StellantisAuth>();
    public DbSet<StellantisOcorrencia> StellantisOcorrencia => Set<StellantisOcorrencia>();
    public DbSet<DatacobInstallmentInsertion> DatacobInstallmentInsertion => Set<DatacobInstallmentInsertion>();
}
